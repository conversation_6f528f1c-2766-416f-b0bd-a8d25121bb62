<Window x:Class="MediaPlayerApp.SimpleMainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="🎬 مشغل الوسائط البسيط - Simple Media Player" 
        Height="500" Width="700"
        WindowStartupLocation="CenterScreen"
        Background="#FF1E1E1E">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Title -->
        <TextBlock Grid.Row="0" Text="🎬 مشغل الوسائط البسيط 2025"
                  FontSize="16" FontWeight="Bold" Foreground="White"
                  HorizontalAlignment="Center" Margin="10"/>

        <!-- Video Area -->
        <Border Grid.Row="1" Background="Black" Margin="10" CornerRadius="5">
            <Grid>
                <MediaElement x:Name="mediaPlayer"
                             LoadedBehavior="Manual"
                             UnloadedBehavior="Stop"
                             Stretch="Uniform"/>
                
                <TextBlock Text="اضغط فتح ملف للبدء"
                          HorizontalAlignment="Center" VerticalAlignment="Center"
                          Foreground="White" FontSize="14"
                          x:Name="welcomeText"/>
            </Grid>
        </Border>

        <!-- Progress -->
        <Grid Grid.Row="2" Margin="10,5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock x:Name="currentTime" Grid.Column="0" Text="00:00" 
                      Foreground="White" VerticalAlignment="Center" Margin="0,0,10,0"/>
            
            <Slider x:Name="progressSlider" Grid.Column="1" Minimum="0" Maximum="100"/>
            
            <TextBlock x:Name="totalTime" Grid.Column="2" Text="00:00" 
                      Foreground="White" VerticalAlignment="Center" Margin="10,0,0,0"/>
        </Grid>

        <!-- Controls -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="10">
            <Button Content="📁 فتح" Width="60" Height="35" Margin="5"
                   Background="#FF404040" Foreground="White" BorderBrush="Transparent"
                   Click="OpenFile_Click"/>
            
            <Button x:Name="playButton" Content="▶️" Width="50" Height="35" Margin="5"
                   Background="#FF007ACC" Foreground="White" BorderBrush="Transparent"
                   Click="PlayPause_Click"/>
            
            <Button Content="⏹️" Width="40" Height="35" Margin="5"
                   Background="#FF404040" Foreground="White" BorderBrush="Transparent"
                   Click="Stop_Click"/>
            
            <Slider x:Name="volumeSlider" Width="80" Minimum="0" Maximum="1" Value="0.5"
                   VerticalAlignment="Center" Margin="10,0"/>
            
            <TextBlock x:Name="volumeText" Text="50%" Foreground="White" 
                      VerticalAlignment="Center" Margin="5,0"/>
        </StackPanel>
    </Grid>
</Window>
