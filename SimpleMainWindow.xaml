<Window x:Class="MediaPlayerApp.SimpleMainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="🎬 مشغل الوسائط المتطور 2025 - Advanced Media Player 2025"
        Height="800" Width="1400"
        MinHeight="600" MinWidth="1000"
        WindowStartupLocation="CenterScreen"
        Background="Transparent"
        WindowStyle="None"
        AllowsTransparency="True"
        ResizeMode="CanResize">

    <!-- Modern Glass Effect Background -->
    <Border Background="#E6000000" CornerRadius="15">
        <Border.Effect>
            <DropShadowEffect Color="Black" BlurRadius="30" ShadowDepth="0" Opacity="0.8"/>
        </Border.Effect>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Modern Title Bar with Controls -->
            <Border Grid.Row="0" Background="Transparent" Height="50" MouseLeftButtonDown="TitleBar_MouseLeftButtonDown">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- App Icon and Title -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                        <Ellipse Width="30" Height="30" Margin="0,0,10,0">
                            <Ellipse.Fill>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                    <GradientStop Color="#FF00D4FF" Offset="0"/>
                                    <GradientStop Color="#FF0099CC" Offset="1"/>
                                </LinearGradientBrush>
                            </Ellipse.Fill>
                        </Ellipse>
                        <TextBlock Text="🎬" FontSize="20" VerticalAlignment="Center" Margin="0,0,8,0"/>
                        <TextBlock Text="مشغل الوسائط المتطور 2025" FontSize="14" FontWeight="Bold"
                                  Foreground="White" VerticalAlignment="Center"/>
                    </StackPanel>

                    <!-- File Info -->
                    <TextBlock x:Name="fileInfoText" Grid.Column="1" Text="جاهز للتشغيل - Ready to Play"
                              FontSize="12" Foreground="#FFAAAAAA" VerticalAlignment="Center"
                              HorizontalAlignment="Center"/>

                    <!-- Window Controls -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                        <Button Content="🔄" Width="35" Height="35" Margin="5,0" Background="Transparent"
                               Foreground="White" BorderBrush="Transparent" Click="Refresh_Click"
                               ToolTip="تحديث - Refresh"/>
                        <Button Content="⚙️" Width="35" Height="35" Margin="5,0" Background="Transparent"
                               Foreground="White" BorderBrush="Transparent" Click="Settings_Click"
                               ToolTip="الإعدادات - Settings"/>
                        <Button Content="🔳" Width="35" Height="35" Margin="5,0" Background="Transparent"
                               Foreground="White" BorderBrush="Transparent" Click="Minimize_Click"
                               ToolTip="تصغير - Minimize"/>
                        <Button Content="🔲" Width="35" Height="35" Margin="5,0" Background="Transparent"
                               Foreground="White" BorderBrush="Transparent" Click="Maximize_Click"
                               ToolTip="تكبير - Maximize"/>
                        <Button Content="❌" Width="35" Height="35" Margin="5,0" Background="#FFDC3545"
                               Foreground="White" BorderBrush="Transparent" Click="Close_Click"
                               ToolTip="إغلاق - Close"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- Modern Menu Bar -->
            <Border Grid.Row="1" Background="#33FFFFFF" Height="45" Margin="10,0">
                <Border.Effect>
                    <BlurEffect Radius="10"/>
                </Border.Effect>
                <StackPanel Orientation="Horizontal" VerticalAlignment="Center" HorizontalAlignment="Center">
                    <Button Content="📁 فتح ملفات" Width="100" Height="30" Margin="10,0"
                           Background="#FF007ACC" Foreground="White" BorderBrush="Transparent"
                           Click="OpenFile_Click" ToolTip="فتح ملفات متعددة"/>
                    <Button Content="📋 قائمة التشغيل" Width="120" Height="30" Margin="10,0"
                           Background="#FF28A745" Foreground="White" BorderBrush="Transparent"
                           Click="Playlist_Click" ToolTip="إدارة قائمة التشغيل"/>
                    <Button Content="🎵 المعادل" Width="100" Height="30" Margin="10,0"
                           Background="#FF6F42C1" Foreground="White" BorderBrush="Transparent"
                           Click="Equalizer_Click" ToolTip="معادل الصوت"/>
                    <Button Content="🎨 المظهر" Width="100" Height="30" Margin="10,0"
                           Background="#FFFD7E14" Foreground="White" BorderBrush="Transparent"
                           Click="Theme_Click" ToolTip="تغيير المظهر"/>
                    <Button Content="📺 ملء الشاشة" Width="120" Height="30" Margin="10,0"
                           Background="#FF20C997" Foreground="White" BorderBrush="Transparent"
                           Click="Fullscreen_Click" ToolTip="وضع ملء الشاشة"/>
                </StackPanel>
            </Border>

            <!-- Advanced Video Display Area -->
            <Border Grid.Row="2" Background="#FF0A0A0A" Margin="15" CornerRadius="10">
                <Border.Effect>
                    <DropShadowEffect Color="Black" BlurRadius="20" ShadowDepth="5" Opacity="0.6"/>
                </Border.Effect>

                <Grid>
                    <MediaElement x:Name="mediaPlayer"
                                 LoadedBehavior="Manual"
                                 UnloadedBehavior="Stop"
                                 Stretch="Uniform"/>

                    <!-- Advanced Welcome Screen -->
                    <Grid x:Name="welcomeScreen">
                        <Grid.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#FF1A1A1A" Offset="0"/>
                                <GradientStop Color="#FF2D2D30" Offset="1"/>
                            </LinearGradientBrush>
                        </Grid.Background>

                        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                            <!-- Animated Icon -->
                            <Ellipse Width="120" Height="120" Margin="0,0,0,30">
                                <Ellipse.Fill>
                                    <RadialGradientBrush>
                                        <GradientStop Color="#FF00D4FF" Offset="0"/>
                                        <GradientStop Color="#FF0099CC" Offset="0.7"/>
                                        <GradientStop Color="#FF006699" Offset="1"/>
                                    </RadialGradientBrush>
                                </Ellipse.Fill>
                                <Ellipse.Effect>
                                    <DropShadowEffect Color="#FF00D4FF" BlurRadius="20" ShadowDepth="0"/>
                                </Ellipse.Effect>
                            </Ellipse>

                            <TextBlock Text="🎬" FontSize="80" HorizontalAlignment="Center"
                                      Foreground="White" Margin="0,-90,0,20"/>

                            <TextBlock Text="مشغل الوسائط المتطور 2025" FontSize="28" FontWeight="Bold"
                                      Foreground="White" HorizontalAlignment="Center" Margin="0,0,0,10"/>

                            <TextBlock Text="Advanced Media Player 2025" FontSize="18"
                                      Foreground="#FFCCCCCC" HorizontalAlignment="Center" Margin="0,0,0,20"/>

                            <TextBlock Text="✨ مميزات متطورة | AI Enhanced | 4K Support ✨" FontSize="14"
                                      Foreground="#FF00D4FF" HorizontalAlignment="Center" Margin="0,0,0,15"/>

                            <TextBlock Text="اسحب الملفات هنا أو اضغط فتح ملفات للبدء" FontSize="16"
                                      Foreground="#FF888888" HorizontalAlignment="Center" Margin="0,0,0,5"/>

                            <TextBlock Text="Drag files here or click Open Files to start" FontSize="14"
                                      Foreground="#FF666666" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Grid>

                    <!-- Video Overlay Controls -->
                    <Grid x:Name="videoOverlay" Visibility="Collapsed" Background="#80000000">
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                            <Button Content="⏮️" Width="60" Height="60" Margin="10" Background="#80FFFFFF"
                                   Foreground="White" BorderBrush="Transparent" Click="Previous_Click"/>
                            <Button x:Name="overlayPlayButton" Content="▶️" Width="80" Height="80" Margin="10"
                                   Background="#FF007ACC" Foreground="White" BorderBrush="Transparent"
                                   Click="PlayPause_Click" FontSize="30"/>
                            <Button Content="⏭️" Width="60" Height="60" Margin="10" Background="#80FFFFFF"
                                   Foreground="White" BorderBrush="Transparent" Click="Next_Click"/>
                        </StackPanel>
                    </Grid>
                </Grid>
            </Border>

            <!-- Advanced Progress Bar -->
            <Border Grid.Row="3" Background="#33FFFFFF" Margin="15,5" CornerRadius="8" Padding="15,10">
                <Border.Effect>
                    <BlurEffect Radius="5"/>
                </Border.Effect>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock x:Name="currentTime" Grid.Column="0" Text="00:00"
                              Foreground="White" VerticalAlignment="Center" Margin="0,0,15,0"
                              FontFamily="Consolas" FontSize="14" FontWeight="Bold"/>

                    <Grid Grid.Column="1">
                        <Border Background="#FF333333" Height="8" CornerRadius="4"/>
                        <Border x:Name="progressBackground" Background="#FF007ACC" Height="8"
                               CornerRadius="4" HorizontalAlignment="Left" Width="0"/>
                        <Slider x:Name="progressSlider" Minimum="0" Maximum="100"
                               Background="Transparent" Height="20" VerticalAlignment="Center"/>
                    </Grid>

                    <TextBlock x:Name="totalTime" Grid.Column="2" Text="00:00"
                              Foreground="White" VerticalAlignment="Center" Margin="15,0,20,0"
                              FontFamily="Consolas" FontSize="14" FontWeight="Bold"/>

                    <StackPanel Grid.Column="3" Orientation="Horizontal">
                        <TextBlock Text="🔊" VerticalAlignment="Center" Foreground="White" FontSize="16" Margin="0,0,8,0"/>
                        <Slider x:Name="volumeSlider" Width="100" Minimum="0" Maximum="1" Value="0.5"
                               VerticalAlignment="Center" Height="20"/>
                        <TextBlock x:Name="volumeText" Text="50%" Foreground="White"
                                  VerticalAlignment="Center" Margin="8,0,0,0" FontWeight="Bold" Width="35"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- Advanced Control Panel -->
            <Border Grid.Row="4" Background="#33FFFFFF" Margin="15,5" CornerRadius="8" Height="80">
                <Border.Effect>
                    <BlurEffect Radius="5"/>
                </Border.Effect>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Left Side Features -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center"
                               HorizontalAlignment="Left" Margin="20,0">
                        <Button Content="🔀" Width="45" Height="45" Margin="5" Background="#FF6C757D"
                               Foreground="White" BorderBrush="Transparent" Click="Shuffle_Click"
                               ToolTip="خلط عشوائي - Shuffle"/>
                        <Button Content="🔁" Width="45" Height="45" Margin="5" Background="#FF6C757D"
                               Foreground="White" BorderBrush="Transparent" Click="Repeat_Click"
                               ToolTip="تكرار - Repeat"/>
                        <Button Content="⭐" Width="45" Height="45" Margin="5" Background="#FFFFC107"
                               Foreground="White" BorderBrush="Transparent" Click="Favorite_Click"
                               ToolTip="إضافة للمفضلة - Add to Favorites"/>
                    </StackPanel>

                    <!-- Center Control Buttons -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center"
                               HorizontalAlignment="Center">
                        <Button Content="⏮️" Width="50" Height="50" Margin="8" Background="#FF495057"
                               Foreground="White" BorderBrush="Transparent" Click="Previous_Click"
                               ToolTip="السابق - Previous"/>

                        <Button x:Name="playButton" Content="▶️" Width="70" Height="70" Margin="8"
                               Background="#FF007ACC" Foreground="White" BorderBrush="Transparent"
                               Click="PlayPause_Click" ToolTip="تشغيل/إيقاف - Play/Pause" FontSize="24">
                            <Button.Effect>
                                <DropShadowEffect Color="#FF007ACC" BlurRadius="15" ShadowDepth="0"/>
                            </Button.Effect>
                        </Button>

                        <Button Content="⏹️" Width="50" Height="50" Margin="8" Background="#FFDC3545"
                               Foreground="White" BorderBrush="Transparent" Click="Stop_Click"
                               ToolTip="إيقاف - Stop"/>

                        <Button Content="⏭️" Width="50" Height="50" Margin="8" Background="#FF495057"
                               Foreground="White" BorderBrush="Transparent" Click="Next_Click"
                               ToolTip="التالي - Next"/>
                    </StackPanel>

                    <!-- Right Side Advanced Features -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center"
                               HorizontalAlignment="Right" Margin="20,0">
                        <Button Content="🎯" Width="45" Height="45" Margin="5" Background="#FF17A2B8"
                               Foreground="White" BorderBrush="Transparent" Click="Subtitle_Click"
                               ToolTip="الترجمة - Subtitles"/>
                        <Button Content="🎨" Width="45" Height="45" Margin="5" Background="#FF6F42C1"
                               Foreground="White" BorderBrush="Transparent" Click="Filter_Click"
                               ToolTip="المرشحات - Filters"/>
                        <Button Content="📱" Width="45" Height="45" Margin="5" Background="#FF20C997"
                               Foreground="White" BorderBrush="Transparent" Click="Cast_Click"
                               ToolTip="البث - Cast"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- Status Bar -->
            <Border Grid.Row="5" Background="#33FFFFFF" Margin="15,5,15,15" CornerRadius="8" Height="35">
                <Border.Effect>
                    <BlurEffect Radius="3"/>
                </Border.Effect>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="15,0">
                        <Ellipse Width="10" Height="10" Fill="#FF28A745" Margin="0,0,8,0"/>
                        <TextBlock x:Name="statusText" Text="جاهز - Ready" Foreground="White" FontSize="12"/>
                    </StackPanel>

                    <TextBlock x:Name="playlistInfo" Grid.Column="1" Text="قائمة التشغيل: 0 ملف"
                              Foreground="#FFAAAAAA" FontSize="12" VerticalAlignment="Center"
                              HorizontalAlignment="Center"/>

                    <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center" Margin="15,0">
                        <TextBlock Text="🎵" Foreground="White" FontSize="12" Margin="0,0,5,0"/>
                        <TextBlock x:Name="qualityText" Text="HD Ready" Foreground="#FF00D4FF" FontSize="12"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>
    </Border>
</Window>
