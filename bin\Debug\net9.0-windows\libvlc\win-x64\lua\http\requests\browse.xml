<?xml version="1.0" encoding="utf-8" standalone="yes" ?>
<?vlc --[[
vim:syntax=lua
<!--  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - >
<  browse.xml: VLC media player web interface
< - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - >
<  Copyright (C) 2005-2006 the VideoLAN team
< 
<  Authors: <AUTHORS>
< 
<  This program is free software; you can redistribute it and/or modify
<  it under the terms of the GNU General Public License as published by
<  the Free Software Foundation; either version 2 of the License, or
<  (at your option) any later version.
< 
<  This program is distributed in the hope that it will be useful,
<  but WITHOUT ANY WARRANTY; without even the implied warranty of
<  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
<  GNU General Public License for more details.
< 
<  You should have received a copy of the GNU General Public License
<  along with this program; if not, write to the Free Software
<  Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston MA 02110-1301, USA.
< - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -->
]] ?>

<?vlc

--package.loaded.httprequests = nil --uncomment to debug changes
local httprequests = require "httprequests"

httprequests.processcommands()

local browseTable=httprequests.getbrowsetable()

print('<root>\n')

--httprequests.printTableAsJson(browseTable.element._array,0)


for i,e in ipairs(browseTable.element._array) do
	print('\n<element ')

	for k,v in pairs(e) do
		print(" "..httprequests.xmlString(k).."='"..httprequests.xmlString(v).."'")
	end

	print('/>')
end


print('\n</root>')

?>

