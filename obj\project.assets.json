{"version": 3, "targets": {"net9.0-windows7.0": {"LibVLCSharp/3.8.0": {"type": "package", "compile": {"lib/net6.0/LibVLCSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/LibVLCSharp.dll": {"related": ".xml"}}}, "LibVLCSharp.WPF/3.8.0": {"type": "package", "dependencies": {"LibVLCSharp": "3.8.0"}, "compile": {"lib/net6.0-windows7.0/LibVLCSharp.WPF.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows7.0/LibVLCSharp.WPF.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "VideoLAN.LibVLC.Windows/3.0.18": {"type": "package", "build": {"build/VideoLAN.LibVLC.Windows.targets": {}}}}}, "libraries": {"LibVLCSharp/3.8.0": {"sha512": "vHHnH+nu3M8fb4jSt4Ad4aVOq7trW4ZBh85WFp7QaWPWDUX9SFVcgYc5IKOtyNh1IaBM/OmZV6NuvTz6eDv6tw==", "type": "package", "path": "libvlcsharp/3.8.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/monoandroid81/LibVLCSharp.Android.AWindow.dll", "lib/monoandroid81/LibVLCSharp.dll", "lib/monoandroid81/LibVLCSharp.xml", "lib/net40/LibVLCSharp.dll", "lib/net40/LibVLCSharp.xml", "lib/net471/LibVLCSharp.dll", "lib/net471/LibVLCSharp.xml", "lib/net6.0-android31.0/LibVLCSharp.Android.AWindow.dll", "lib/net6.0-android31.0/LibVLCSharp.dll", "lib/net6.0-android31.0/LibVLCSharp.xml", "lib/net6.0-ios16.1/LibVLCSharp.dll", "lib/net6.0-ios16.1/LibVLCSharp.xml", "lib/net6.0-macos13.0/LibVLCSharp.dll", "lib/net6.0-macos13.0/LibVLCSharp.xml", "lib/net6.0-tvos16.1/LibVLCSharp.dll", "lib/net6.0-tvos16.1/LibVLCSharp.xml", "lib/net6.0-windows10.0.17763/LibVLCSharp.dll", "lib/net6.0-windows10.0.17763/LibVLCSharp.pri", "lib/net6.0-windows10.0.17763/LibVLCSharp.xml", "lib/net6.0/LibVLCSharp.dll", "lib/net6.0/LibVLCSharp.xml", "lib/netstandard1.1/LibVLCSharp.dll", "lib/netstandard1.1/LibVLCSharp.xml", "lib/netstandard2.0/LibVLCSharp.dll", "lib/netstandard2.0/LibVLCSharp.xml", "lib/netstandard2.1/LibVLCSharp.dll", "lib/netstandard2.1/LibVLCSharp.xml", "lib/uap10.0.18362/LibVLCSharp.dll", "lib/uap10.0.18362/LibVLCSharp.pri", "lib/uap10.0.18362/LibVLCSharp.xml", "lib/uap10.0.18362/LibVLCSharp/LibVLCSharp.xr.xml", "lib/xamarinios10/LibVLCSharp.dll", "lib/xamarinios10/LibVLCSharp.xml", "lib/xamarinmac20/LibVLCSharp.dll", "lib/xamarinmac20/LibVLCSharp.xml", "libvlcsharp.3.8.0.nupkg.sha512", "libvlcsharp.nuspec"]}, "LibVLCSharp.WPF/3.8.0": {"sha512": "6AaIfxhPYH5gh9GbFZtz7rjsDZpxx0XS7V94tGK8bkBZzE0ZUpFD/3Zo0lsVsZCMY7nTE+e0wBsVW5uAZYL7Dg==", "type": "package", "path": "libvlcsharp.wpf/3.8.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net461/LibVLCSharp.WPF.dll", "lib/net461/LibVLCSharp.WPF.xml", "lib/net6.0-windows7.0/LibVLCSharp.WPF.dll", "lib/net6.0-windows7.0/LibVLCSharp.WPF.xml", "lib/netcoreapp3.1/LibVLCSharp.WPF.dll", "lib/netcoreapp3.1/LibVLCSharp.WPF.xml", "libvlcsharp.wpf.3.8.0.nupkg.sha512", "libvlcsharp.wpf.nuspec"]}, "VideoLAN.LibVLC.Windows/3.0.18": {"sha512": "A7Fg86aSziadDxB4F8AWN6kHNgEYJqerdPG9SflPM0GDO9m/O2jnc60mfSa52xOh5EC4U62W0y4kdyw+FgB2iQ==", "type": "package", "path": "videolan.libvlc.windows/3.0.18", "files": [".nupkg.metadata", ".signature.p7s", "build/VideoLAN.LibVLC.Windows.targets", "build/x64/hrtfs/dodeca_and_7channel_3DSL_HRTF.sofa", "build/x64/include/vlc/deprecated.h", "build/x64/include/vlc/libvlc.h", "build/x64/include/vlc/libvlc_dialog.h", "build/x64/include/vlc/libvlc_events.h", "build/x64/include/vlc/libvlc_media.h", "build/x64/include/vlc/libvlc_media_discoverer.h", "build/x64/include/vlc/libvlc_media_library.h", "build/x64/include/vlc/libvlc_media_list.h", "build/x64/include/vlc/libvlc_media_list_player.h", "build/x64/include/vlc/libvlc_media_player.h", "build/x64/include/vlc/libvlc_renderer_discoverer.h", "build/x64/include/vlc/libvlc_version.h", "build/x64/include/vlc/libvlc_vlm.h", "build/x64/include/vlc/plugins/vlc_about.h", "build/x64/include/vlc/plugins/vlc_access.h", "build/x64/include/vlc/plugins/vlc_actions.h", "build/x64/include/vlc/plugins/vlc_addons.h", "build/x64/include/vlc/plugins/vlc_aout.h", "build/x64/include/vlc/plugins/vlc_aout_volume.h", "build/x64/include/vlc/plugins/vlc_arrays.h", "build/x64/include/vlc/plugins/vlc_atomic.h", "build/x64/include/vlc/plugins/vlc_avcodec.h", "build/x64/include/vlc/plugins/vlc_bits.h", "build/x64/include/vlc/plugins/vlc_block.h", "build/x64/include/vlc/plugins/vlc_block_helper.h", "build/x64/include/vlc/plugins/vlc_boxes.h", "build/x64/include/vlc/plugins/vlc_charset.h", "build/x64/include/vlc/plugins/vlc_codec.h", "build/x64/include/vlc/plugins/vlc_common.h", "build/x64/include/vlc/plugins/vlc_config.h", "build/x64/include/vlc/plugins/vlc_config_cat.h", "build/x64/include/vlc/plugins/vlc_configuration.h", "build/x64/include/vlc/plugins/vlc_cpu.h", "build/x64/include/vlc/plugins/vlc_demux.h", "build/x64/include/vlc/plugins/vlc_dialog.h", "build/x64/include/vlc/plugins/vlc_epg.h", "build/x64/include/vlc/plugins/vlc_es.h", "build/x64/include/vlc/plugins/vlc_es_out.h", "build/x64/include/vlc/plugins/vlc_events.h", "build/x64/include/vlc/plugins/vlc_filter.h", "build/x64/include/vlc/plugins/vlc_fingerprinter.h", "build/x64/include/vlc/plugins/vlc_fourcc.h", "build/x64/include/vlc/plugins/vlc_fs.h", "build/x64/include/vlc/plugins/vlc_gcrypt.h", "build/x64/include/vlc/plugins/vlc_http.h", "build/x64/include/vlc/plugins/vlc_httpd.h", "build/x64/include/vlc/plugins/vlc_image.h", "build/x64/include/vlc/plugins/vlc_inhibit.h", "build/x64/include/vlc/plugins/vlc_input.h", "build/x64/include/vlc/plugins/vlc_input_item.h", "build/x64/include/vlc/plugins/vlc_interface.h", "build/x64/include/vlc/plugins/vlc_interrupt.h", "build/x64/include/vlc/plugins/vlc_keystore.h", "build/x64/include/vlc/plugins/vlc_main.h", "build/x64/include/vlc/plugins/vlc_md5.h", "build/x64/include/vlc/plugins/vlc_media_library.h", "build/x64/include/vlc/plugins/vlc_memstream.h", "build/x64/include/vlc/plugins/vlc_messages.h", "build/x64/include/vlc/plugins/vlc_meta.h", "build/x64/include/vlc/plugins/vlc_meta_fetcher.h", "build/x64/include/vlc/plugins/vlc_mime.h", "build/x64/include/vlc/plugins/vlc_modules.h", "build/x64/include/vlc/plugins/vlc_mouse.h", "build/x64/include/vlc/plugins/vlc_mtime.h", "build/x64/include/vlc/plugins/vlc_network.h", "build/x64/include/vlc/plugins/vlc_objects.h", "build/x64/include/vlc/plugins/vlc_opengl.h", "build/x64/include/vlc/plugins/vlc_picture.h", "build/x64/include/vlc/plugins/vlc_picture_fifo.h", "build/x64/include/vlc/plugins/vlc_picture_pool.h", "build/x64/include/vlc/plugins/vlc_playlist.h", "build/x64/include/vlc/plugins/vlc_plugin.h", "build/x64/include/vlc/plugins/vlc_probe.h", "build/x64/include/vlc/plugins/vlc_rand.h", "build/x64/include/vlc/plugins/vlc_renderer_discovery.h", "build/x64/include/vlc/plugins/vlc_services_discovery.h", "build/x64/include/vlc/plugins/vlc_sout.h", "build/x64/include/vlc/plugins/vlc_spu.h", "build/x64/include/vlc/plugins/vlc_stream.h", "build/x64/include/vlc/plugins/vlc_stream_extractor.h", "build/x64/include/vlc/plugins/vlc_strings.h", "build/x64/include/vlc/plugins/vlc_subpicture.h", "build/x64/include/vlc/plugins/vlc_text_style.h", "build/x64/include/vlc/plugins/vlc_threads.h", "build/x64/include/vlc/plugins/vlc_timestamp_helper.h", "build/x64/include/vlc/plugins/vlc_tls.h", "build/x64/include/vlc/plugins/vlc_url.h", "build/x64/include/vlc/plugins/vlc_variables.h", "build/x64/include/vlc/plugins/vlc_video_splitter.h", "build/x64/include/vlc/plugins/vlc_viewpoint.h", "build/x64/include/vlc/plugins/vlc_vlm.h", "build/x64/include/vlc/plugins/vlc_vout.h", "build/x64/include/vlc/plugins/vlc_vout_display.h", "build/x64/include/vlc/plugins/vlc_vout_osd.h", "build/x64/include/vlc/plugins/vlc_vout_window.h", "build/x64/include/vlc/plugins/vlc_xlib.h", "build/x64/include/vlc/plugins/vlc_xml.h", "build/x64/include/vlc/vlc.h", "build/x64/libvlc.dll", "build/x64/libvlc.lib", "build/x64/libvlccore.dll", "build/x64/libvlccore.lib", "build/x64/locale/ach/LC_MESSAGES/vlc.mo", "build/x64/locale/af/LC_MESSAGES/vlc.mo", "build/x64/locale/am/LC_MESSAGES/vlc.mo", "build/x64/locale/am_ET/LC_MESSAGES/vlc.mo", "build/x64/locale/an/LC_MESSAGES/vlc.mo", "build/x64/locale/ar/LC_MESSAGES/vlc.mo", "build/x64/locale/as_IN/LC_MESSAGES/vlc.mo", "build/x64/locale/ast/LC_MESSAGES/vlc.mo", "build/x64/locale/be/LC_MESSAGES/vlc.mo", "build/x64/locale/bg/LC_MESSAGES/vlc.mo", "build/x64/locale/bn/LC_MESSAGES/vlc.mo", "build/x64/locale/bn_IN/LC_MESSAGES/vlc.mo", "build/x64/locale/br/LC_MESSAGES/vlc.mo", "build/x64/locale/brx/LC_MESSAGES/vlc.mo", "build/x64/locale/bs/LC_MESSAGES/vlc.mo", "build/x64/locale/ca/LC_MESSAGES/vlc.mo", "build/x64/locale/ca@valencia/LC_MESSAGES/vlc.mo", "build/x64/locale/cgg/LC_MESSAGES/vlc.mo", "build/x64/locale/co/LC_MESSAGES/vlc.mo", "build/x64/locale/cs/LC_MESSAGES/vlc.mo", "build/x64/locale/cy/LC_MESSAGES/vlc.mo", "build/x64/locale/da/LC_MESSAGES/vlc.mo", "build/x64/locale/de/LC_MESSAGES/vlc.mo", "build/x64/locale/el/LC_MESSAGES/vlc.mo", "build/x64/locale/en_GB/LC_MESSAGES/vlc.mo", "build/x64/locale/es/LC_MESSAGES/vlc.mo", "build/x64/locale/es_MX/LC_MESSAGES/vlc.mo", "build/x64/locale/et/LC_MESSAGES/vlc.mo", "build/x64/locale/eu/LC_MESSAGES/vlc.mo", "build/x64/locale/fa/LC_MESSAGES/vlc.mo", "build/x64/locale/ff/LC_MESSAGES/vlc.mo", "build/x64/locale/fi/LC_MESSAGES/vlc.mo", "build/x64/locale/fr/LC_MESSAGES/vlc.mo", "build/x64/locale/fur/LC_MESSAGES/vlc.mo", "build/x64/locale/fy/LC_MESSAGES/vlc.mo", "build/x64/locale/ga/LC_MESSAGES/vlc.mo", "build/x64/locale/gd/LC_MESSAGES/vlc.mo", "build/x64/locale/gl/LC_MESSAGES/vlc.mo", "build/x64/locale/gu/LC_MESSAGES/vlc.mo", "build/x64/locale/he/LC_MESSAGES/vlc.mo", "build/x64/locale/hi/LC_MESSAGES/vlc.mo", "build/x64/locale/hr/LC_MESSAGES/vlc.mo", "build/x64/locale/hu/LC_MESSAGES/vlc.mo", "build/x64/locale/hy/LC_MESSAGES/vlc.mo", "build/x64/locale/id/LC_MESSAGES/vlc.mo", "build/x64/locale/is/LC_MESSAGES/vlc.mo", "build/x64/locale/it/LC_MESSAGES/vlc.mo", "build/x64/locale/ja/LC_MESSAGES/vlc.mo", "build/x64/locale/ka/LC_MESSAGES/vlc.mo", "build/x64/locale/kab/LC_MESSAGES/vlc.mo", "build/x64/locale/kk/LC_MESSAGES/vlc.mo", "build/x64/locale/km/LC_MESSAGES/vlc.mo", "build/x64/locale/kn/LC_MESSAGES/vlc.mo", "build/x64/locale/ko/LC_MESSAGES/vlc.mo", "build/x64/locale/ks_IN/LC_MESSAGES/vlc.mo", "build/x64/locale/ku_IQ/LC_MESSAGES/vlc.mo", "build/x64/locale/ky/LC_MESSAGES/vlc.mo", "build/x64/locale/lg/LC_MESSAGES/vlc.mo", "build/x64/locale/lt/LC_MESSAGES/vlc.mo", "build/x64/locale/lv/LC_MESSAGES/vlc.mo", "build/x64/locale/mai/LC_MESSAGES/vlc.mo", "build/x64/locale/ml/LC_MESSAGES/vlc.mo", "build/x64/locale/mn/LC_MESSAGES/vlc.mo", "build/x64/locale/mr/LC_MESSAGES/vlc.mo", "build/x64/locale/ms/LC_MESSAGES/vlc.mo", "build/x64/locale/nb/LC_MESSAGES/vlc.mo", "build/x64/locale/ne/LC_MESSAGES/vlc.mo", "build/x64/locale/nl/LC_MESSAGES/vlc.mo", "build/x64/locale/nn/LC_MESSAGES/vlc.mo", "build/x64/locale/oc/LC_MESSAGES/vlc.mo", "build/x64/locale/pa/LC_MESSAGES/vlc.mo", "build/x64/locale/pl/LC_MESSAGES/vlc.mo", "build/x64/locale/ps/LC_MESSAGES/vlc.mo", "build/x64/locale/pt_BR/LC_MESSAGES/vlc.mo", "build/x64/locale/pt_PT/LC_MESSAGES/vlc.mo", "build/x64/locale/ro/LC_MESSAGES/vlc.mo", "build/x64/locale/ru/LC_MESSAGES/vlc.mo", "build/x64/locale/si/LC_MESSAGES/vlc.mo", "build/x64/locale/sk/LC_MESSAGES/vlc.mo", "build/x64/locale/sl/LC_MESSAGES/vlc.mo", "build/x64/locale/sq/LC_MESSAGES/vlc.mo", "build/x64/locale/sr/LC_MESSAGES/vlc.mo", "build/x64/locale/sv/LC_MESSAGES/vlc.mo", "build/x64/locale/ta/LC_MESSAGES/vlc.mo", "build/x64/locale/te/LC_MESSAGES/vlc.mo", "build/x64/locale/th/LC_MESSAGES/vlc.mo", "build/x64/locale/tr/LC_MESSAGES/vlc.mo", "build/x64/locale/tt/LC_MESSAGES/vlc.mo", "build/x64/locale/ug/LC_MESSAGES/vlc.mo", "build/x64/locale/uk/LC_MESSAGES/vlc.mo", "build/x64/locale/uz/LC_MESSAGES/vlc.mo", "build/x64/locale/vi/LC_MESSAGES/vlc.mo", "build/x64/locale/wa/LC_MESSAGES/vlc.mo", "build/x64/locale/zh_CN/LC_MESSAGES/vlc.mo", "build/x64/locale/zh_TW/LC_MESSAGES/vlc.mo", "build/x64/locale/zu/LC_MESSAGES/vlc.mo", "build/x64/lua/extensions/VLSub.luac", "build/x64/lua/http/css/main.css", "build/x64/lua/http/css/mobile.css", "build/x64/lua/http/css/ui-lightness/images/ui-bg_diagonals-thick_18_b81900_40x40.png", "build/x64/lua/http/css/ui-lightness/images/ui-bg_diagonals-thick_20_666666_40x40.png", "build/x64/lua/http/css/ui-lightness/images/ui-bg_flat_10_000000_40x100.png", "build/x64/lua/http/css/ui-lightness/images/ui-bg_glass_100_f6f6f6_1x400.png", "build/x64/lua/http/css/ui-lightness/images/ui-bg_glass_100_fdf5ce_1x400.png", "build/x64/lua/http/css/ui-lightness/images/ui-bg_glass_65_ffffff_1x400.png", "build/x64/lua/http/css/ui-lightness/images/ui-bg_gloss-wave_35_f6a828_500x100.png", "build/x64/lua/http/css/ui-lightness/images/ui-bg_highlight-soft_100_eeeeee_1x100.png", "build/x64/lua/http/css/ui-lightness/images/ui-bg_highlight-soft_75_ffe45c_1x100.png", "build/x64/lua/http/css/ui-lightness/images/ui-icons_222222_256x240.png", "build/x64/lua/http/css/ui-lightness/images/ui-icons_228ef1_256x240.png", "build/x64/lua/http/css/ui-lightness/images/ui-icons_ef8c08_256x240.png", "build/x64/lua/http/css/ui-lightness/images/ui-icons_ffd27a_256x240.png", "build/x64/lua/http/css/ui-lightness/images/ui-icons_ffffff_256x240.png", "build/x64/lua/http/css/ui-lightness/jquery-ui-1.8.13.custom.css", "build/x64/lua/http/custom.lua", "build/x64/lua/http/dialogs/batch_window.html", "build/x64/lua/http/dialogs/browse_window.html", "build/x64/lua/http/dialogs/create_stream.html", "build/x64/lua/http/dialogs/equalizer_window.html", "build/x64/lua/http/dialogs/error_window.html", "build/x64/lua/http/dialogs/mosaic_window.html", "build/x64/lua/http/dialogs/offset_window.html", "build/x64/lua/http/dialogs/stream_config_window.html", "build/x64/lua/http/dialogs/stream_window.html", "build/x64/lua/http/favicon.ico", "build/x64/lua/http/images/Audio-48.png", "build/x64/lua/http/images/Back-48.png", "build/x64/lua/http/images/Folder-48.png", "build/x64/lua/http/images/Other-48.png", "build/x64/lua/http/images/Video-48.png", "build/x64/lua/http/images/buttons.png", "build/x64/lua/http/images/speaker-32.png", "build/x64/lua/http/images/vlc-48.png", "build/x64/lua/http/images/vlc16x16.png", "build/x64/lua/http/index.html", "build/x64/lua/http/js/common.js", "build/x64/lua/http/js/controllers.js", "build/x64/lua/http/js/jquery.jstree.js", "build/x64/lua/http/js/ui.js", "build/x64/lua/http/mobile.html", "build/x64/lua/http/mobile_browse.html", "build/x64/lua/http/mobile_equalizer.html", "build/x64/lua/http/mobile_view.html", "build/x64/lua/http/requests/README.txt", "build/x64/lua/http/requests/browse.json", "build/x64/lua/http/requests/browse.xml", "build/x64/lua/http/requests/playlist.json", "build/x64/lua/http/requests/playlist.xml", "build/x64/lua/http/requests/playlist_jstree.xml", "build/x64/lua/http/requests/status.json", "build/x64/lua/http/requests/status.xml", "build/x64/lua/http/requests/vlm.xml", "build/x64/lua/http/requests/vlm_cmd.xml", "build/x64/lua/http/view.html", "build/x64/lua/http/vlm.html", "build/x64/lua/http/vlm_export.html", "build/x64/lua/intf/cli.luac", "build/x64/lua/intf/dummy.luac", "build/x64/lua/intf/dumpmeta.luac", "build/x64/lua/intf/http.luac", "build/x64/lua/intf/luac.luac", "build/x64/lua/intf/modules/host.luac", "build/x64/lua/intf/modules/httprequests.luac", "build/x64/lua/intf/telnet.luac", "build/x64/lua/meta/art/00_musicbrainz.luac", "build/x64/lua/meta/art/01_googleimage.luac", "build/x64/lua/meta/art/02_frenchtv.luac", "build/x64/lua/meta/art/03_lastfm.luac", "build/x64/lua/meta/reader/filename.luac", "build/x64/lua/modules/common.luac", "build/x64/lua/modules/dkjson.luac", "build/x64/lua/modules/sandbox.luac", "build/x64/lua/modules/simplexml.luac", "build/x64/lua/playlist/anevia_streams.luac", "build/x64/lua/playlist/anevia_xml.luac", "build/x64/lua/playlist/appletrailers.luac", "build/x64/lua/playlist/bbc_co_uk.luac", "build/x64/lua/playlist/cue.luac", "build/x64/lua/playlist/dailymotion.luac", "build/x64/lua/playlist/jamendo.luac", "build/x64/lua/playlist/koreus.luac", "build/x64/lua/playlist/liveleak.luac", "build/x64/lua/playlist/newgrounds.luac", "build/x64/lua/playlist/rockbox_fm_presets.luac", "build/x64/lua/playlist/soundcloud.luac", "build/x64/lua/playlist/twitch.luac", "build/x64/lua/playlist/vimeo.luac", "build/x64/lua/playlist/vocaroo.luac", "build/x64/lua/playlist/youtube.luac", "build/x64/lua/sd/icecast.luac", "build/x64/lua/sd/jamendo.luac", "build/x64/plugins/access/libaccess_concat_plugin.dll", "build/x64/plugins/access/libaccess_imem_plugin.dll", "build/x64/plugins/access/libaccess_mms_plugin.dll", "build/x64/plugins/access/libaccess_realrtsp_plugin.dll", "build/x64/plugins/access/libaccess_srt_plugin.dll", "build/x64/plugins/access/libaccess_wasapi_plugin.dll", "build/x64/plugins/access/libattachment_plugin.dll", "build/x64/plugins/access/libbluray-awt-j2se-1.3.2.jar", "build/x64/plugins/access/libbluray-j2se-1.3.2.jar", "build/x64/plugins/access/libcdda_plugin.dll", "build/x64/plugins/access/libdcp_plugin.dll", "build/x64/plugins/access/libdshow_plugin.dll", "build/x64/plugins/access/libdtv_plugin.dll", "build/x64/plugins/access/libdvdnav_plugin.dll", "build/x64/plugins/access/libdvdread_plugin.dll", "build/x64/plugins/access/libfilesystem_plugin.dll", "build/x64/plugins/access/libftp_plugin.dll", "build/x64/plugins/access/libhttp_plugin.dll", "build/x64/plugins/access/libhttps_plugin.dll", "build/x64/plugins/access/libidummy_plugin.dll", "build/x64/plugins/access/libimem_plugin.dll", "build/x64/plugins/access/liblibbluray_plugin.dll", "build/x64/plugins/access/liblive555_plugin.dll", "build/x64/plugins/access/libnfs_plugin.dll", "build/x64/plugins/access/librist_plugin.dll", "build/x64/plugins/access/librtp_plugin.dll", "build/x64/plugins/access/libsatip_plugin.dll", "build/x64/plugins/access/libscreen_plugin.dll", "build/x64/plugins/access/libsdp_plugin.dll", "build/x64/plugins/access/libsftp_plugin.dll", "build/x64/plugins/access/libshm_plugin.dll", "build/x64/plugins/access/libsmb_plugin.dll", "build/x64/plugins/access/libtcp_plugin.dll", "build/x64/plugins/access/libtimecode_plugin.dll", "build/x64/plugins/access/libudp_plugin.dll", "build/x64/plugins/access/libvcd_plugin.dll", "build/x64/plugins/access/libvdr_plugin.dll", "build/x64/plugins/access/libvnc_plugin.dll", "build/x64/plugins/access_output/libaccess_output_dummy_plugin.dll", "build/x64/plugins/access_output/libaccess_output_file_plugin.dll", "build/x64/plugins/access_output/libaccess_output_http_plugin.dll", "build/x64/plugins/access_output/libaccess_output_livehttp_plugin.dll", "build/x64/plugins/access_output/libaccess_output_rist_plugin.dll", "build/x64/plugins/access_output/libaccess_output_shout_plugin.dll", "build/x64/plugins/access_output/libaccess_output_srt_plugin.dll", "build/x64/plugins/access_output/libaccess_output_udp_plugin.dll", "build/x64/plugins/audio_filter/libaudio_format_plugin.dll", "build/x64/plugins/audio_filter/libaudiobargraph_a_plugin.dll", "build/x64/plugins/audio_filter/libchorus_flanger_plugin.dll", "build/x64/plugins/audio_filter/libcompressor_plugin.dll", "build/x64/plugins/audio_filter/libdolby_surround_decoder_plugin.dll", "build/x64/plugins/audio_filter/libequalizer_plugin.dll", "build/x64/plugins/audio_filter/libgain_plugin.dll", "build/x64/plugins/audio_filter/libheadphone_channel_mixer_plugin.dll", "build/x64/plugins/audio_filter/libkaraoke_plugin.dll", "build/x64/plugins/audio_filter/libmad_plugin.dll", "build/x64/plugins/audio_filter/libmono_plugin.dll", "build/x64/plugins/audio_filter/libnormvol_plugin.dll", "build/x64/plugins/audio_filter/libparam_eq_plugin.dll", "build/x64/plugins/audio_filter/libremap_plugin.dll", "build/x64/plugins/audio_filter/libsamplerate_plugin.dll", "build/x64/plugins/audio_filter/libscaletempo_pitch_plugin.dll", "build/x64/plugins/audio_filter/libscaletempo_plugin.dll", "build/x64/plugins/audio_filter/libsimple_channel_mixer_plugin.dll", "build/x64/plugins/audio_filter/libspatialaudio_plugin.dll", "build/x64/plugins/audio_filter/libspatializer_plugin.dll", "build/x64/plugins/audio_filter/libspeex_resampler_plugin.dll", "build/x64/plugins/audio_filter/libstereo_widen_plugin.dll", "build/x64/plugins/audio_filter/libtospdif_plugin.dll", "build/x64/plugins/audio_filter/libtrivial_channel_mixer_plugin.dll", "build/x64/plugins/audio_filter/libugly_resampler_plugin.dll", "build/x64/plugins/audio_mixer/libfloat_mixer_plugin.dll", "build/x64/plugins/audio_mixer/libinteger_mixer_plugin.dll", "build/x64/plugins/audio_output/libadummy_plugin.dll", "build/x64/plugins/audio_output/libafile_plugin.dll", "build/x64/plugins/audio_output/libamem_plugin.dll", "build/x64/plugins/audio_output/libdirectsound_plugin.dll", "build/x64/plugins/audio_output/libmmdevice_plugin.dll", "build/x64/plugins/audio_output/libwasapi_plugin.dll", "build/x64/plugins/audio_output/libwaveout_plugin.dll", "build/x64/plugins/codec/liba52_plugin.dll", "build/x64/plugins/codec/libadpcm_plugin.dll", "build/x64/plugins/codec/libaes3_plugin.dll", "build/x64/plugins/codec/libaom_plugin.dll", "build/x64/plugins/codec/libaraw_plugin.dll", "build/x64/plugins/codec/libaribsub_plugin.dll", "build/x64/plugins/codec/libavcodec_plugin.dll", "build/x64/plugins/codec/libcc_plugin.dll", "build/x64/plugins/codec/libcdg_plugin.dll", "build/x64/plugins/codec/libcrystalhd_plugin.dll", "build/x64/plugins/codec/libcvdsub_plugin.dll", "build/x64/plugins/codec/libd3d11va_plugin.dll", "build/x64/plugins/codec/libdav1d_plugin.dll", "build/x64/plugins/codec/libdca_plugin.dll", "build/x64/plugins/codec/libddummy_plugin.dll", "build/x64/plugins/codec/libdmo_plugin.dll", "build/x64/plugins/codec/libdvbsub_plugin.dll", "build/x64/plugins/codec/libdxva2_plugin.dll", "build/x64/plugins/codec/libedummy_plugin.dll", "build/x64/plugins/codec/libfaad_plugin.dll", "build/x64/plugins/codec/libflac_plugin.dll", "build/x64/plugins/codec/libfluidsynth_plugin.dll", "build/x64/plugins/codec/libg711_plugin.dll", "build/x64/plugins/codec/libjpeg_plugin.dll", "build/x64/plugins/codec/libkate_plugin.dll", "build/x64/plugins/codec/liblibass_plugin.dll", "build/x64/plugins/codec/liblibmpeg2_plugin.dll", "build/x64/plugins/codec/liblpcm_plugin.dll", "build/x64/plugins/codec/libmft_plugin.dll", "build/x64/plugins/codec/libmpg123_plugin.dll", "build/x64/plugins/codec/liboggspots_plugin.dll", "build/x64/plugins/codec/libopus_plugin.dll", "build/x64/plugins/codec/libpng_plugin.dll", "build/x64/plugins/codec/libqsv_plugin.dll", "build/x64/plugins/codec/librawvideo_plugin.dll", "build/x64/plugins/codec/librtpvideo_plugin.dll", "build/x64/plugins/codec/libschroedinger_plugin.dll", "build/x64/plugins/codec/libscte18_plugin.dll", "build/x64/plugins/codec/libscte27_plugin.dll", "build/x64/plugins/codec/libsdl_image_plugin.dll", "build/x64/plugins/codec/libspdif_plugin.dll", "build/x64/plugins/codec/libspeex_plugin.dll", "build/x64/plugins/codec/libspudec_plugin.dll", "build/x64/plugins/codec/libstl_plugin.dll", "build/x64/plugins/codec/libsubsdec_plugin.dll", "build/x64/plugins/codec/libsubstx3g_plugin.dll", "build/x64/plugins/codec/libsubsusf_plugin.dll", "build/x64/plugins/codec/libsvcdsub_plugin.dll", "build/x64/plugins/codec/libt140_plugin.dll", "build/x64/plugins/codec/libtextst_plugin.dll", "build/x64/plugins/codec/libtheora_plugin.dll", "build/x64/plugins/codec/libttml_plugin.dll", "build/x64/plugins/codec/libtwolame_plugin.dll", "build/x64/plugins/codec/libuleaddvaudio_plugin.dll", "build/x64/plugins/codec/libvorbis_plugin.dll", "build/x64/plugins/codec/libvpx_plugin.dll", "build/x64/plugins/codec/libwebvtt_plugin.dll", "build/x64/plugins/codec/libx26410b_plugin.dll", "build/x64/plugins/codec/libx264_plugin.dll", "build/x64/plugins/codec/libx265_plugin.dll", "build/x64/plugins/codec/libzvbi_plugin.dll", "build/x64/plugins/control/libdummy_plugin.dll", "build/x64/plugins/control/libgestures_plugin.dll", "build/x64/plugins/control/libhotkeys_plugin.dll", "build/x64/plugins/control/libnetsync_plugin.dll", "build/x64/plugins/control/libntservice_plugin.dll", "build/x64/plugins/control/liboldrc_plugin.dll", "build/x64/plugins/control/libwin_hotkeys_plugin.dll", "build/x64/plugins/control/libwin_msg_plugin.dll", "build/x64/plugins/d3d11/libdirect3d11_filters_plugin.dll", "build/x64/plugins/d3d9/libdirect3d9_filters_plugin.dll", "build/x64/plugins/demux/libadaptive_plugin.dll", "build/x64/plugins/demux/libaiff_plugin.dll", "build/x64/plugins/demux/libasf_plugin.dll", "build/x64/plugins/demux/libau_plugin.dll", "build/x64/plugins/demux/libavi_plugin.dll", "build/x64/plugins/demux/libcaf_plugin.dll", "build/x64/plugins/demux/libdemux_cdg_plugin.dll", "build/x64/plugins/demux/libdemux_chromecast_plugin.dll", "build/x64/plugins/demux/libdemux_stl_plugin.dll", "build/x64/plugins/demux/libdemuxdump_plugin.dll", "build/x64/plugins/demux/libdiracsys_plugin.dll", "build/x64/plugins/demux/libdirectory_demux_plugin.dll", "build/x64/plugins/demux/libes_plugin.dll", "build/x64/plugins/demux/libflacsys_plugin.dll", "build/x64/plugins/demux/libgme_plugin.dll", "build/x64/plugins/demux/libh26x_plugin.dll", "build/x64/plugins/demux/libimage_plugin.dll", "build/x64/plugins/demux/libmjpeg_plugin.dll", "build/x64/plugins/demux/libmkv_plugin.dll", "build/x64/plugins/demux/libmod_plugin.dll", "build/x64/plugins/demux/libmp4_plugin.dll", "build/x64/plugins/demux/libmpc_plugin.dll", "build/x64/plugins/demux/libmpgv_plugin.dll", "build/x64/plugins/demux/libnoseek_plugin.dll", "build/x64/plugins/demux/libnsc_plugin.dll", "build/x64/plugins/demux/libnsv_plugin.dll", "build/x64/plugins/demux/libnuv_plugin.dll", "build/x64/plugins/demux/libogg_plugin.dll", "build/x64/plugins/demux/libplaylist_plugin.dll", "build/x64/plugins/demux/libps_plugin.dll", "build/x64/plugins/demux/libpva_plugin.dll", "build/x64/plugins/demux/librawaud_plugin.dll", "build/x64/plugins/demux/librawdv_plugin.dll", "build/x64/plugins/demux/librawvid_plugin.dll", "build/x64/plugins/demux/libreal_plugin.dll", "build/x64/plugins/demux/libsid_plugin.dll", "build/x64/plugins/demux/libsmf_plugin.dll", "build/x64/plugins/demux/libsubtitle_plugin.dll", "build/x64/plugins/demux/libts_plugin.dll", "build/x64/plugins/demux/libtta_plugin.dll", "build/x64/plugins/demux/libty_plugin.dll", "build/x64/plugins/demux/libvc1_plugin.dll", "build/x64/plugins/demux/libvobsub_plugin.dll", "build/x64/plugins/demux/libvoc_plugin.dll", "build/x64/plugins/demux/libwav_plugin.dll", "build/x64/plugins/demux/libxa_plugin.dll", "build/x64/plugins/gui/libqt_plugin.dll", "build/x64/plugins/gui/libskins2_plugin.dll", "build/x64/plugins/keystore/libfile_keystore_plugin.dll", "build/x64/plugins/keystore/libmemory_keystore_plugin.dll", "build/x64/plugins/logger/libconsole_logger_plugin.dll", "build/x64/plugins/logger/libfile_logger_plugin.dll", "build/x64/plugins/lua/liblua_plugin.dll", "build/x64/plugins/meta_engine/libfolder_plugin.dll", "build/x64/plugins/meta_engine/libtaglib_plugin.dll", "build/x64/plugins/misc/libaddonsfsstorage_plugin.dll", "build/x64/plugins/misc/libaddonsvorepository_plugin.dll", "build/x64/plugins/misc/libaudioscrobbler_plugin.dll", "build/x64/plugins/misc/libexport_plugin.dll", "build/x64/plugins/misc/libfingerprinter_plugin.dll", "build/x64/plugins/misc/libgnutls_plugin.dll", "build/x64/plugins/misc/liblogger_plugin.dll", "build/x64/plugins/misc/libstats_plugin.dll", "build/x64/plugins/misc/libvod_rtsp_plugin.dll", "build/x64/plugins/misc/libxml_plugin.dll", "build/x64/plugins/mux/libmux_asf_plugin.dll", "build/x64/plugins/mux/libmux_avi_plugin.dll", "build/x64/plugins/mux/libmux_dummy_plugin.dll", "build/x64/plugins/mux/libmux_mp4_plugin.dll", "build/x64/plugins/mux/libmux_mpjpeg_plugin.dll", "build/x64/plugins/mux/libmux_ogg_plugin.dll", "build/x64/plugins/mux/libmux_ps_plugin.dll", "build/x64/plugins/mux/libmux_ts_plugin.dll", "build/x64/plugins/mux/libmux_wav_plugin.dll", "build/x64/plugins/packetizer/libpacketizer_a52_plugin.dll", "build/x64/plugins/packetizer/libpacketizer_av1_plugin.dll", "build/x64/plugins/packetizer/libpacketizer_copy_plugin.dll", "build/x64/plugins/packetizer/libpacketizer_dirac_plugin.dll", "build/x64/plugins/packetizer/libpacketizer_dts_plugin.dll", "build/x64/plugins/packetizer/libpacketizer_flac_plugin.dll", "build/x64/plugins/packetizer/libpacketizer_h264_plugin.dll", "build/x64/plugins/packetizer/libpacketizer_hevc_plugin.dll", "build/x64/plugins/packetizer/libpacketizer_mlp_plugin.dll", "build/x64/plugins/packetizer/libpacketizer_mpeg4audio_plugin.dll", "build/x64/plugins/packetizer/libpacketizer_mpeg4video_plugin.dll", "build/x64/plugins/packetizer/libpacketizer_mpegaudio_plugin.dll", "build/x64/plugins/packetizer/libpacketizer_mpegvideo_plugin.dll", "build/x64/plugins/packetizer/libpacketizer_vc1_plugin.dll", "build/x64/plugins/services_discovery/libmediadirs_plugin.dll", "build/x64/plugins/services_discovery/libmicrodns_plugin.dll", "build/x64/plugins/services_discovery/libpodcast_plugin.dll", "build/x64/plugins/services_discovery/libsap_plugin.dll", "build/x64/plugins/services_discovery/libupnp_plugin.dll", "build/x64/plugins/services_discovery/libwindrive_plugin.dll", "build/x64/plugins/spu/libaudiobargraph_v_plugin.dll", "build/x64/plugins/spu/liblogo_plugin.dll", "build/x64/plugins/spu/libmarq_plugin.dll", "build/x64/plugins/spu/libmosaic_plugin.dll", "build/x64/plugins/spu/libremoteosd_plugin.dll", "build/x64/plugins/spu/librss_plugin.dll", "build/x64/plugins/spu/libsubsdelay_plugin.dll", "build/x64/plugins/stream_extractor/libarchive_plugin.dll", "build/x64/plugins/stream_filter/libadf_plugin.dll", "build/x64/plugins/stream_filter/libaribcam_plugin.dll", "build/x64/plugins/stream_filter/libcache_block_plugin.dll", "build/x64/plugins/stream_filter/libcache_read_plugin.dll", "build/x64/plugins/stream_filter/libhds_plugin.dll", "build/x64/plugins/stream_filter/libinflate_plugin.dll", "build/x64/plugins/stream_filter/libprefetch_plugin.dll", "build/x64/plugins/stream_filter/librecord_plugin.dll", "build/x64/plugins/stream_filter/libskiptags_plugin.dll", "build/x64/plugins/stream_out/libstream_out_autodel_plugin.dll", "build/x64/plugins/stream_out/libstream_out_bridge_plugin.dll", "build/x64/plugins/stream_out/libstream_out_chromaprint_plugin.dll", "build/x64/plugins/stream_out/libstream_out_chromecast_plugin.dll", "build/x64/plugins/stream_out/libstream_out_cycle_plugin.dll", "build/x64/plugins/stream_out/libstream_out_delay_plugin.dll", "build/x64/plugins/stream_out/libstream_out_description_plugin.dll", "build/x64/plugins/stream_out/libstream_out_display_plugin.dll", "build/x64/plugins/stream_out/libstream_out_dummy_plugin.dll", "build/x64/plugins/stream_out/libstream_out_duplicate_plugin.dll", "build/x64/plugins/stream_out/libstream_out_es_plugin.dll", "build/x64/plugins/stream_out/libstream_out_gather_plugin.dll", "build/x64/plugins/stream_out/libstream_out_mosaic_bridge_plugin.dll", "build/x64/plugins/stream_out/libstream_out_record_plugin.dll", "build/x64/plugins/stream_out/libstream_out_rtp_plugin.dll", "build/x64/plugins/stream_out/libstream_out_setid_plugin.dll", "build/x64/plugins/stream_out/libstream_out_smem_plugin.dll", "build/x64/plugins/stream_out/libstream_out_standard_plugin.dll", "build/x64/plugins/stream_out/libstream_out_stats_plugin.dll", "build/x64/plugins/stream_out/libstream_out_transcode_plugin.dll", "build/x64/plugins/text_renderer/libfreetype_plugin.dll", "build/x64/plugins/text_renderer/libsapi_plugin.dll", "build/x64/plugins/text_renderer/libtdummy_plugin.dll", "build/x64/plugins/video_chroma/libchain_plugin.dll", "build/x64/plugins/video_chroma/libgrey_yuv_plugin.dll", "build/x64/plugins/video_chroma/libi420_10_p010_plugin.dll", "build/x64/plugins/video_chroma/libi420_nv12_plugin.dll", "build/x64/plugins/video_chroma/libi420_rgb_mmx_plugin.dll", "build/x64/plugins/video_chroma/libi420_rgb_plugin.dll", "build/x64/plugins/video_chroma/libi420_rgb_sse2_plugin.dll", "build/x64/plugins/video_chroma/libi420_yuy2_mmx_plugin.dll", "build/x64/plugins/video_chroma/libi420_yuy2_plugin.dll", "build/x64/plugins/video_chroma/libi420_yuy2_sse2_plugin.dll", "build/x64/plugins/video_chroma/libi422_i420_plugin.dll", "build/x64/plugins/video_chroma/libi422_yuy2_mmx_plugin.dll", "build/x64/plugins/video_chroma/libi422_yuy2_plugin.dll", "build/x64/plugins/video_chroma/libi422_yuy2_sse2_plugin.dll", "build/x64/plugins/video_chroma/librv32_plugin.dll", "build/x64/plugins/video_chroma/libswscale_plugin.dll", "build/x64/plugins/video_chroma/libyuvp_plugin.dll", "build/x64/plugins/video_chroma/libyuy2_i420_plugin.dll", "build/x64/plugins/video_chroma/libyuy2_i422_plugin.dll", "build/x64/plugins/video_filter/libadjust_plugin.dll", "build/x64/plugins/video_filter/libalphamask_plugin.dll", "build/x64/plugins/video_filter/libanaglyph_plugin.dll", "build/x64/plugins/video_filter/libantiflicker_plugin.dll", "build/x64/plugins/video_filter/libball_plugin.dll", "build/x64/plugins/video_filter/libblend_plugin.dll", "build/x64/plugins/video_filter/libblendbench_plugin.dll", "build/x64/plugins/video_filter/libbluescreen_plugin.dll", "build/x64/plugins/video_filter/libcanvas_plugin.dll", "build/x64/plugins/video_filter/libcolorthres_plugin.dll", "build/x64/plugins/video_filter/libcroppadd_plugin.dll", "build/x64/plugins/video_filter/libdeinterlace_plugin.dll", "build/x64/plugins/video_filter/libedgedetection_plugin.dll", "build/x64/plugins/video_filter/liberase_plugin.dll", "build/x64/plugins/video_filter/libextract_plugin.dll", "build/x64/plugins/video_filter/libfps_plugin.dll", "build/x64/plugins/video_filter/libfreeze_plugin.dll", "build/x64/plugins/video_filter/libgaussianblur_plugin.dll", "build/x64/plugins/video_filter/libgradfun_plugin.dll", "build/x64/plugins/video_filter/libgradient_plugin.dll", "build/x64/plugins/video_filter/libgrain_plugin.dll", "build/x64/plugins/video_filter/libhqdn3d_plugin.dll", "build/x64/plugins/video_filter/libinvert_plugin.dll", "build/x64/plugins/video_filter/libmagnify_plugin.dll", "build/x64/plugins/video_filter/libmirror_plugin.dll", "build/x64/plugins/video_filter/libmotionblur_plugin.dll", "build/x64/plugins/video_filter/libmotiondetect_plugin.dll", "build/x64/plugins/video_filter/liboldmovie_plugin.dll", "build/x64/plugins/video_filter/libposterize_plugin.dll", "build/x64/plugins/video_filter/libpostproc_plugin.dll", "build/x64/plugins/video_filter/libpsychedelic_plugin.dll", "build/x64/plugins/video_filter/libpuzzle_plugin.dll", "build/x64/plugins/video_filter/libripple_plugin.dll", "build/x64/plugins/video_filter/librotate_plugin.dll", "build/x64/plugins/video_filter/libscale_plugin.dll", "build/x64/plugins/video_filter/libscene_plugin.dll", "build/x64/plugins/video_filter/libsepia_plugin.dll", "build/x64/plugins/video_filter/libsharpen_plugin.dll", "build/x64/plugins/video_filter/libtransform_plugin.dll", "build/x64/plugins/video_filter/libvhs_plugin.dll", "build/x64/plugins/video_filter/libwave_plugin.dll", "build/x64/plugins/video_output/libcaca_plugin.dll", "build/x64/plugins/video_output/libdirect3d11_plugin.dll", "build/x64/plugins/video_output/libdirect3d9_plugin.dll", "build/x64/plugins/video_output/libdirectdraw_plugin.dll", "build/x64/plugins/video_output/libdrawable_plugin.dll", "build/x64/plugins/video_output/libflaschen_plugin.dll", "build/x64/plugins/video_output/libgl_plugin.dll", "build/x64/plugins/video_output/libglwin32_plugin.dll", "build/x64/plugins/video_output/libvdummy_plugin.dll", "build/x64/plugins/video_output/libvmem_plugin.dll", "build/x64/plugins/video_output/libwgl_plugin.dll", "build/x64/plugins/video_output/libwingdi_plugin.dll", "build/x64/plugins/video_output/libwinhibit_plugin.dll", "build/x64/plugins/video_output/libyuv_plugin.dll", "build/x64/plugins/video_splitter/libclone_plugin.dll", "build/x64/plugins/video_splitter/libpanoramix_plugin.dll", "build/x64/plugins/video_splitter/libwall_plugin.dll", "build/x64/plugins/visualization/libglspectrum_plugin.dll", "build/x64/plugins/visualization/libgoom_plugin.dll", "build/x64/plugins/visualization/libprojectm_plugin.dll", "build/x64/plugins/visualization/libvisual_plugin.dll", "build/x64/vlc.lib", "build/x64/vlccore.lib", "build/x86/hrtfs/dodeca_and_7channel_3DSL_HRTF.sofa", "build/x86/include/vlc/deprecated.h", "build/x86/include/vlc/libvlc.h", "build/x86/include/vlc/libvlc_dialog.h", "build/x86/include/vlc/libvlc_events.h", "build/x86/include/vlc/libvlc_media.h", "build/x86/include/vlc/libvlc_media_discoverer.h", "build/x86/include/vlc/libvlc_media_library.h", "build/x86/include/vlc/libvlc_media_list.h", "build/x86/include/vlc/libvlc_media_list_player.h", "build/x86/include/vlc/libvlc_media_player.h", "build/x86/include/vlc/libvlc_renderer_discoverer.h", "build/x86/include/vlc/libvlc_version.h", "build/x86/include/vlc/libvlc_vlm.h", "build/x86/include/vlc/plugins/vlc_about.h", "build/x86/include/vlc/plugins/vlc_access.h", "build/x86/include/vlc/plugins/vlc_actions.h", "build/x86/include/vlc/plugins/vlc_addons.h", "build/x86/include/vlc/plugins/vlc_aout.h", "build/x86/include/vlc/plugins/vlc_aout_volume.h", "build/x86/include/vlc/plugins/vlc_arrays.h", "build/x86/include/vlc/plugins/vlc_atomic.h", "build/x86/include/vlc/plugins/vlc_avcodec.h", "build/x86/include/vlc/plugins/vlc_bits.h", "build/x86/include/vlc/plugins/vlc_block.h", "build/x86/include/vlc/plugins/vlc_block_helper.h", "build/x86/include/vlc/plugins/vlc_boxes.h", "build/x86/include/vlc/plugins/vlc_charset.h", "build/x86/include/vlc/plugins/vlc_codec.h", "build/x86/include/vlc/plugins/vlc_common.h", "build/x86/include/vlc/plugins/vlc_config.h", "build/x86/include/vlc/plugins/vlc_config_cat.h", "build/x86/include/vlc/plugins/vlc_configuration.h", "build/x86/include/vlc/plugins/vlc_cpu.h", "build/x86/include/vlc/plugins/vlc_demux.h", "build/x86/include/vlc/plugins/vlc_dialog.h", "build/x86/include/vlc/plugins/vlc_epg.h", "build/x86/include/vlc/plugins/vlc_es.h", "build/x86/include/vlc/plugins/vlc_es_out.h", "build/x86/include/vlc/plugins/vlc_events.h", "build/x86/include/vlc/plugins/vlc_filter.h", "build/x86/include/vlc/plugins/vlc_fingerprinter.h", "build/x86/include/vlc/plugins/vlc_fourcc.h", "build/x86/include/vlc/plugins/vlc_fs.h", "build/x86/include/vlc/plugins/vlc_gcrypt.h", "build/x86/include/vlc/plugins/vlc_http.h", "build/x86/include/vlc/plugins/vlc_httpd.h", "build/x86/include/vlc/plugins/vlc_image.h", "build/x86/include/vlc/plugins/vlc_inhibit.h", "build/x86/include/vlc/plugins/vlc_input.h", "build/x86/include/vlc/plugins/vlc_input_item.h", "build/x86/include/vlc/plugins/vlc_interface.h", "build/x86/include/vlc/plugins/vlc_interrupt.h", "build/x86/include/vlc/plugins/vlc_keystore.h", "build/x86/include/vlc/plugins/vlc_main.h", "build/x86/include/vlc/plugins/vlc_md5.h", "build/x86/include/vlc/plugins/vlc_media_library.h", "build/x86/include/vlc/plugins/vlc_memstream.h", "build/x86/include/vlc/plugins/vlc_messages.h", "build/x86/include/vlc/plugins/vlc_meta.h", "build/x86/include/vlc/plugins/vlc_meta_fetcher.h", "build/x86/include/vlc/plugins/vlc_mime.h", "build/x86/include/vlc/plugins/vlc_modules.h", "build/x86/include/vlc/plugins/vlc_mouse.h", "build/x86/include/vlc/plugins/vlc_mtime.h", "build/x86/include/vlc/plugins/vlc_network.h", "build/x86/include/vlc/plugins/vlc_objects.h", "build/x86/include/vlc/plugins/vlc_opengl.h", "build/x86/include/vlc/plugins/vlc_picture.h", "build/x86/include/vlc/plugins/vlc_picture_fifo.h", "build/x86/include/vlc/plugins/vlc_picture_pool.h", "build/x86/include/vlc/plugins/vlc_playlist.h", "build/x86/include/vlc/plugins/vlc_plugin.h", "build/x86/include/vlc/plugins/vlc_probe.h", "build/x86/include/vlc/plugins/vlc_rand.h", "build/x86/include/vlc/plugins/vlc_renderer_discovery.h", "build/x86/include/vlc/plugins/vlc_services_discovery.h", "build/x86/include/vlc/plugins/vlc_sout.h", "build/x86/include/vlc/plugins/vlc_spu.h", "build/x86/include/vlc/plugins/vlc_stream.h", "build/x86/include/vlc/plugins/vlc_stream_extractor.h", "build/x86/include/vlc/plugins/vlc_strings.h", "build/x86/include/vlc/plugins/vlc_subpicture.h", "build/x86/include/vlc/plugins/vlc_text_style.h", "build/x86/include/vlc/plugins/vlc_threads.h", "build/x86/include/vlc/plugins/vlc_timestamp_helper.h", "build/x86/include/vlc/plugins/vlc_tls.h", "build/x86/include/vlc/plugins/vlc_url.h", "build/x86/include/vlc/plugins/vlc_variables.h", "build/x86/include/vlc/plugins/vlc_video_splitter.h", "build/x86/include/vlc/plugins/vlc_viewpoint.h", "build/x86/include/vlc/plugins/vlc_vlm.h", "build/x86/include/vlc/plugins/vlc_vout.h", "build/x86/include/vlc/plugins/vlc_vout_display.h", "build/x86/include/vlc/plugins/vlc_vout_osd.h", "build/x86/include/vlc/plugins/vlc_vout_window.h", "build/x86/include/vlc/plugins/vlc_xlib.h", "build/x86/include/vlc/plugins/vlc_xml.h", "build/x86/include/vlc/vlc.h", "build/x86/libvlc.dll", "build/x86/libvlc.lib", "build/x86/libvlccore.dll", "build/x86/libvlccore.lib", "build/x86/locale/ach/LC_MESSAGES/vlc.mo", "build/x86/locale/af/LC_MESSAGES/vlc.mo", "build/x86/locale/am/LC_MESSAGES/vlc.mo", "build/x86/locale/am_ET/LC_MESSAGES/vlc.mo", "build/x86/locale/an/LC_MESSAGES/vlc.mo", "build/x86/locale/ar/LC_MESSAGES/vlc.mo", "build/x86/locale/as_IN/LC_MESSAGES/vlc.mo", "build/x86/locale/ast/LC_MESSAGES/vlc.mo", "build/x86/locale/be/LC_MESSAGES/vlc.mo", "build/x86/locale/bg/LC_MESSAGES/vlc.mo", "build/x86/locale/bn/LC_MESSAGES/vlc.mo", "build/x86/locale/bn_IN/LC_MESSAGES/vlc.mo", "build/x86/locale/br/LC_MESSAGES/vlc.mo", "build/x86/locale/brx/LC_MESSAGES/vlc.mo", "build/x86/locale/bs/LC_MESSAGES/vlc.mo", "build/x86/locale/ca/LC_MESSAGES/vlc.mo", "build/x86/locale/ca@valencia/LC_MESSAGES/vlc.mo", "build/x86/locale/cgg/LC_MESSAGES/vlc.mo", "build/x86/locale/co/LC_MESSAGES/vlc.mo", "build/x86/locale/cs/LC_MESSAGES/vlc.mo", "build/x86/locale/cy/LC_MESSAGES/vlc.mo", "build/x86/locale/da/LC_MESSAGES/vlc.mo", "build/x86/locale/de/LC_MESSAGES/vlc.mo", "build/x86/locale/el/LC_MESSAGES/vlc.mo", "build/x86/locale/en_GB/LC_MESSAGES/vlc.mo", "build/x86/locale/es/LC_MESSAGES/vlc.mo", "build/x86/locale/es_MX/LC_MESSAGES/vlc.mo", "build/x86/locale/et/LC_MESSAGES/vlc.mo", "build/x86/locale/eu/LC_MESSAGES/vlc.mo", "build/x86/locale/fa/LC_MESSAGES/vlc.mo", "build/x86/locale/ff/LC_MESSAGES/vlc.mo", "build/x86/locale/fi/LC_MESSAGES/vlc.mo", "build/x86/locale/fr/LC_MESSAGES/vlc.mo", "build/x86/locale/fur/LC_MESSAGES/vlc.mo", "build/x86/locale/fy/LC_MESSAGES/vlc.mo", "build/x86/locale/ga/LC_MESSAGES/vlc.mo", "build/x86/locale/gd/LC_MESSAGES/vlc.mo", "build/x86/locale/gl/LC_MESSAGES/vlc.mo", "build/x86/locale/gu/LC_MESSAGES/vlc.mo", "build/x86/locale/he/LC_MESSAGES/vlc.mo", "build/x86/locale/hi/LC_MESSAGES/vlc.mo", "build/x86/locale/hr/LC_MESSAGES/vlc.mo", "build/x86/locale/hu/LC_MESSAGES/vlc.mo", "build/x86/locale/hy/LC_MESSAGES/vlc.mo", "build/x86/locale/id/LC_MESSAGES/vlc.mo", "build/x86/locale/is/LC_MESSAGES/vlc.mo", "build/x86/locale/it/LC_MESSAGES/vlc.mo", "build/x86/locale/ja/LC_MESSAGES/vlc.mo", "build/x86/locale/ka/LC_MESSAGES/vlc.mo", "build/x86/locale/kab/LC_MESSAGES/vlc.mo", "build/x86/locale/kk/LC_MESSAGES/vlc.mo", "build/x86/locale/km/LC_MESSAGES/vlc.mo", "build/x86/locale/kn/LC_MESSAGES/vlc.mo", "build/x86/locale/ko/LC_MESSAGES/vlc.mo", "build/x86/locale/ks_IN/LC_MESSAGES/vlc.mo", "build/x86/locale/ku_IQ/LC_MESSAGES/vlc.mo", "build/x86/locale/ky/LC_MESSAGES/vlc.mo", "build/x86/locale/lg/LC_MESSAGES/vlc.mo", "build/x86/locale/lt/LC_MESSAGES/vlc.mo", "build/x86/locale/lv/LC_MESSAGES/vlc.mo", "build/x86/locale/mai/LC_MESSAGES/vlc.mo", "build/x86/locale/ml/LC_MESSAGES/vlc.mo", "build/x86/locale/mn/LC_MESSAGES/vlc.mo", "build/x86/locale/mr/LC_MESSAGES/vlc.mo", "build/x86/locale/ms/LC_MESSAGES/vlc.mo", "build/x86/locale/nb/LC_MESSAGES/vlc.mo", "build/x86/locale/ne/LC_MESSAGES/vlc.mo", "build/x86/locale/nl/LC_MESSAGES/vlc.mo", "build/x86/locale/nn/LC_MESSAGES/vlc.mo", "build/x86/locale/oc/LC_MESSAGES/vlc.mo", "build/x86/locale/pa/LC_MESSAGES/vlc.mo", "build/x86/locale/pl/LC_MESSAGES/vlc.mo", "build/x86/locale/ps/LC_MESSAGES/vlc.mo", "build/x86/locale/pt_BR/LC_MESSAGES/vlc.mo", "build/x86/locale/pt_PT/LC_MESSAGES/vlc.mo", "build/x86/locale/ro/LC_MESSAGES/vlc.mo", "build/x86/locale/ru/LC_MESSAGES/vlc.mo", "build/x86/locale/si/LC_MESSAGES/vlc.mo", "build/x86/locale/sk/LC_MESSAGES/vlc.mo", "build/x86/locale/sl/LC_MESSAGES/vlc.mo", "build/x86/locale/sq/LC_MESSAGES/vlc.mo", "build/x86/locale/sr/LC_MESSAGES/vlc.mo", "build/x86/locale/sv/LC_MESSAGES/vlc.mo", "build/x86/locale/ta/LC_MESSAGES/vlc.mo", "build/x86/locale/te/LC_MESSAGES/vlc.mo", "build/x86/locale/th/LC_MESSAGES/vlc.mo", "build/x86/locale/tr/LC_MESSAGES/vlc.mo", "build/x86/locale/tt/LC_MESSAGES/vlc.mo", "build/x86/locale/ug/LC_MESSAGES/vlc.mo", "build/x86/locale/uk/LC_MESSAGES/vlc.mo", "build/x86/locale/uz/LC_MESSAGES/vlc.mo", "build/x86/locale/vi/LC_MESSAGES/vlc.mo", "build/x86/locale/wa/LC_MESSAGES/vlc.mo", "build/x86/locale/zh_CN/LC_MESSAGES/vlc.mo", "build/x86/locale/zh_TW/LC_MESSAGES/vlc.mo", "build/x86/locale/zu/LC_MESSAGES/vlc.mo", "build/x86/lua/extensions/VLSub.luac", "build/x86/lua/http/css/main.css", "build/x86/lua/http/css/mobile.css", "build/x86/lua/http/css/ui-lightness/images/ui-bg_diagonals-thick_18_b81900_40x40.png", "build/x86/lua/http/css/ui-lightness/images/ui-bg_diagonals-thick_20_666666_40x40.png", "build/x86/lua/http/css/ui-lightness/images/ui-bg_flat_10_000000_40x100.png", "build/x86/lua/http/css/ui-lightness/images/ui-bg_glass_100_f6f6f6_1x400.png", "build/x86/lua/http/css/ui-lightness/images/ui-bg_glass_100_fdf5ce_1x400.png", "build/x86/lua/http/css/ui-lightness/images/ui-bg_glass_65_ffffff_1x400.png", "build/x86/lua/http/css/ui-lightness/images/ui-bg_gloss-wave_35_f6a828_500x100.png", "build/x86/lua/http/css/ui-lightness/images/ui-bg_highlight-soft_100_eeeeee_1x100.png", "build/x86/lua/http/css/ui-lightness/images/ui-bg_highlight-soft_75_ffe45c_1x100.png", "build/x86/lua/http/css/ui-lightness/images/ui-icons_222222_256x240.png", "build/x86/lua/http/css/ui-lightness/images/ui-icons_228ef1_256x240.png", "build/x86/lua/http/css/ui-lightness/images/ui-icons_ef8c08_256x240.png", "build/x86/lua/http/css/ui-lightness/images/ui-icons_ffd27a_256x240.png", "build/x86/lua/http/css/ui-lightness/images/ui-icons_ffffff_256x240.png", "build/x86/lua/http/css/ui-lightness/jquery-ui-1.8.13.custom.css", "build/x86/lua/http/custom.lua", "build/x86/lua/http/dialogs/batch_window.html", "build/x86/lua/http/dialogs/browse_window.html", "build/x86/lua/http/dialogs/create_stream.html", "build/x86/lua/http/dialogs/equalizer_window.html", "build/x86/lua/http/dialogs/error_window.html", "build/x86/lua/http/dialogs/mosaic_window.html", "build/x86/lua/http/dialogs/offset_window.html", "build/x86/lua/http/dialogs/stream_config_window.html", "build/x86/lua/http/dialogs/stream_window.html", "build/x86/lua/http/favicon.ico", "build/x86/lua/http/images/Audio-48.png", "build/x86/lua/http/images/Back-48.png", "build/x86/lua/http/images/Folder-48.png", "build/x86/lua/http/images/Other-48.png", "build/x86/lua/http/images/Video-48.png", "build/x86/lua/http/images/buttons.png", "build/x86/lua/http/images/speaker-32.png", "build/x86/lua/http/images/vlc-48.png", "build/x86/lua/http/images/vlc16x16.png", "build/x86/lua/http/index.html", "build/x86/lua/http/js/common.js", "build/x86/lua/http/js/controllers.js", "build/x86/lua/http/js/jquery.jstree.js", "build/x86/lua/http/js/ui.js", "build/x86/lua/http/mobile.html", "build/x86/lua/http/mobile_browse.html", "build/x86/lua/http/mobile_equalizer.html", "build/x86/lua/http/mobile_view.html", "build/x86/lua/http/requests/README.txt", "build/x86/lua/http/requests/browse.json", "build/x86/lua/http/requests/browse.xml", "build/x86/lua/http/requests/playlist.json", "build/x86/lua/http/requests/playlist.xml", "build/x86/lua/http/requests/playlist_jstree.xml", "build/x86/lua/http/requests/status.json", "build/x86/lua/http/requests/status.xml", "build/x86/lua/http/requests/vlm.xml", "build/x86/lua/http/requests/vlm_cmd.xml", "build/x86/lua/http/view.html", "build/x86/lua/http/vlm.html", "build/x86/lua/http/vlm_export.html", "build/x86/lua/intf/cli.luac", "build/x86/lua/intf/dummy.luac", "build/x86/lua/intf/dumpmeta.luac", "build/x86/lua/intf/http.luac", "build/x86/lua/intf/luac.luac", "build/x86/lua/intf/modules/host.luac", "build/x86/lua/intf/modules/httprequests.luac", "build/x86/lua/intf/telnet.luac", "build/x86/lua/meta/art/00_musicbrainz.luac", "build/x86/lua/meta/art/01_googleimage.luac", "build/x86/lua/meta/art/02_frenchtv.luac", "build/x86/lua/meta/art/03_lastfm.luac", "build/x86/lua/meta/reader/filename.luac", "build/x86/lua/modules/common.luac", "build/x86/lua/modules/dkjson.luac", "build/x86/lua/modules/sandbox.luac", "build/x86/lua/modules/simplexml.luac", "build/x86/lua/playlist/anevia_streams.luac", "build/x86/lua/playlist/anevia_xml.luac", "build/x86/lua/playlist/appletrailers.luac", "build/x86/lua/playlist/bbc_co_uk.luac", "build/x86/lua/playlist/cue.luac", "build/x86/lua/playlist/dailymotion.luac", "build/x86/lua/playlist/jamendo.luac", "build/x86/lua/playlist/koreus.luac", "build/x86/lua/playlist/liveleak.luac", "build/x86/lua/playlist/newgrounds.luac", "build/x86/lua/playlist/rockbox_fm_presets.luac", "build/x86/lua/playlist/soundcloud.luac", "build/x86/lua/playlist/twitch.luac", "build/x86/lua/playlist/vimeo.luac", "build/x86/lua/playlist/vocaroo.luac", "build/x86/lua/playlist/youtube.luac", "build/x86/lua/sd/icecast.luac", "build/x86/lua/sd/jamendo.luac", "build/x86/plugins/access/libaccess_concat_plugin.dll", "build/x86/plugins/access/libaccess_imem_plugin.dll", "build/x86/plugins/access/libaccess_mms_plugin.dll", "build/x86/plugins/access/libaccess_realrtsp_plugin.dll", "build/x86/plugins/access/libaccess_srt_plugin.dll", "build/x86/plugins/access/libaccess_wasapi_plugin.dll", "build/x86/plugins/access/libattachment_plugin.dll", "build/x86/plugins/access/libbluray-awt-j2se-1.3.2.jar", "build/x86/plugins/access/libbluray-j2se-1.3.2.jar", "build/x86/plugins/access/libcdda_plugin.dll", "build/x86/plugins/access/libdcp_plugin.dll", "build/x86/plugins/access/libdshow_plugin.dll", "build/x86/plugins/access/libdtv_plugin.dll", "build/x86/plugins/access/libdvdnav_plugin.dll", "build/x86/plugins/access/libdvdread_plugin.dll", "build/x86/plugins/access/libfilesystem_plugin.dll", "build/x86/plugins/access/libftp_plugin.dll", "build/x86/plugins/access/libhttp_plugin.dll", "build/x86/plugins/access/libhttps_plugin.dll", "build/x86/plugins/access/libidummy_plugin.dll", "build/x86/plugins/access/libimem_plugin.dll", "build/x86/plugins/access/liblibbluray_plugin.dll", "build/x86/plugins/access/liblive555_plugin.dll", "build/x86/plugins/access/libnfs_plugin.dll", "build/x86/plugins/access/librist_plugin.dll", "build/x86/plugins/access/librtp_plugin.dll", "build/x86/plugins/access/libsatip_plugin.dll", "build/x86/plugins/access/libscreen_plugin.dll", "build/x86/plugins/access/libsdp_plugin.dll", "build/x86/plugins/access/libsftp_plugin.dll", "build/x86/plugins/access/libshm_plugin.dll", "build/x86/plugins/access/libsmb_plugin.dll", "build/x86/plugins/access/libtcp_plugin.dll", "build/x86/plugins/access/libtimecode_plugin.dll", "build/x86/plugins/access/libudp_plugin.dll", "build/x86/plugins/access/libvcd_plugin.dll", "build/x86/plugins/access/libvdr_plugin.dll", "build/x86/plugins/access/libvnc_plugin.dll", "build/x86/plugins/access_output/libaccess_output_dummy_plugin.dll", "build/x86/plugins/access_output/libaccess_output_file_plugin.dll", "build/x86/plugins/access_output/libaccess_output_http_plugin.dll", "build/x86/plugins/access_output/libaccess_output_livehttp_plugin.dll", "build/x86/plugins/access_output/libaccess_output_rist_plugin.dll", "build/x86/plugins/access_output/libaccess_output_shout_plugin.dll", "build/x86/plugins/access_output/libaccess_output_srt_plugin.dll", "build/x86/plugins/access_output/libaccess_output_udp_plugin.dll", "build/x86/plugins/audio_filter/libaudio_format_plugin.dll", "build/x86/plugins/audio_filter/libaudiobargraph_a_plugin.dll", "build/x86/plugins/audio_filter/libchorus_flanger_plugin.dll", "build/x86/plugins/audio_filter/libcompressor_plugin.dll", "build/x86/plugins/audio_filter/libdolby_surround_decoder_plugin.dll", "build/x86/plugins/audio_filter/libequalizer_plugin.dll", "build/x86/plugins/audio_filter/libgain_plugin.dll", "build/x86/plugins/audio_filter/libheadphone_channel_mixer_plugin.dll", "build/x86/plugins/audio_filter/libkaraoke_plugin.dll", "build/x86/plugins/audio_filter/libmad_plugin.dll", "build/x86/plugins/audio_filter/libmono_plugin.dll", "build/x86/plugins/audio_filter/libnormvol_plugin.dll", "build/x86/plugins/audio_filter/libparam_eq_plugin.dll", "build/x86/plugins/audio_filter/libremap_plugin.dll", "build/x86/plugins/audio_filter/libsamplerate_plugin.dll", "build/x86/plugins/audio_filter/libscaletempo_pitch_plugin.dll", "build/x86/plugins/audio_filter/libscaletempo_plugin.dll", "build/x86/plugins/audio_filter/libsimple_channel_mixer_plugin.dll", "build/x86/plugins/audio_filter/libspatialaudio_plugin.dll", "build/x86/plugins/audio_filter/libspatializer_plugin.dll", "build/x86/plugins/audio_filter/libspeex_resampler_plugin.dll", "build/x86/plugins/audio_filter/libstereo_widen_plugin.dll", "build/x86/plugins/audio_filter/libtospdif_plugin.dll", "build/x86/plugins/audio_filter/libtrivial_channel_mixer_plugin.dll", "build/x86/plugins/audio_filter/libugly_resampler_plugin.dll", "build/x86/plugins/audio_mixer/libfloat_mixer_plugin.dll", "build/x86/plugins/audio_mixer/libinteger_mixer_plugin.dll", "build/x86/plugins/audio_output/libadummy_plugin.dll", "build/x86/plugins/audio_output/libafile_plugin.dll", "build/x86/plugins/audio_output/libamem_plugin.dll", "build/x86/plugins/audio_output/libdirectsound_plugin.dll", "build/x86/plugins/audio_output/libmmdevice_plugin.dll", "build/x86/plugins/audio_output/libwasapi_plugin.dll", "build/x86/plugins/audio_output/libwaveout_plugin.dll", "build/x86/plugins/codec/liba52_plugin.dll", "build/x86/plugins/codec/libadpcm_plugin.dll", "build/x86/plugins/codec/libaes3_plugin.dll", "build/x86/plugins/codec/libaom_plugin.dll", "build/x86/plugins/codec/libaraw_plugin.dll", "build/x86/plugins/codec/libaribsub_plugin.dll", "build/x86/plugins/codec/libavcodec_plugin.dll", "build/x86/plugins/codec/libcc_plugin.dll", "build/x86/plugins/codec/libcdg_plugin.dll", "build/x86/plugins/codec/libcrystalhd_plugin.dll", "build/x86/plugins/codec/libcvdsub_plugin.dll", "build/x86/plugins/codec/libd3d11va_plugin.dll", "build/x86/plugins/codec/libdav1d_plugin.dll", "build/x86/plugins/codec/libdca_plugin.dll", "build/x86/plugins/codec/libddummy_plugin.dll", "build/x86/plugins/codec/libdmo_plugin.dll", "build/x86/plugins/codec/libdvbsub_plugin.dll", "build/x86/plugins/codec/libdxva2_plugin.dll", "build/x86/plugins/codec/libedummy_plugin.dll", "build/x86/plugins/codec/libfaad_plugin.dll", "build/x86/plugins/codec/libflac_plugin.dll", "build/x86/plugins/codec/libfluidsynth_plugin.dll", "build/x86/plugins/codec/libg711_plugin.dll", "build/x86/plugins/codec/libjpeg_plugin.dll", "build/x86/plugins/codec/libkate_plugin.dll", "build/x86/plugins/codec/liblibass_plugin.dll", "build/x86/plugins/codec/liblibmpeg2_plugin.dll", "build/x86/plugins/codec/liblpcm_plugin.dll", "build/x86/plugins/codec/libmft_plugin.dll", "build/x86/plugins/codec/libmpg123_plugin.dll", "build/x86/plugins/codec/liboggspots_plugin.dll", "build/x86/plugins/codec/libopus_plugin.dll", "build/x86/plugins/codec/libpng_plugin.dll", "build/x86/plugins/codec/libqsv_plugin.dll", "build/x86/plugins/codec/librawvideo_plugin.dll", "build/x86/plugins/codec/librtpvideo_plugin.dll", "build/x86/plugins/codec/libschroedinger_plugin.dll", "build/x86/plugins/codec/libscte18_plugin.dll", "build/x86/plugins/codec/libscte27_plugin.dll", "build/x86/plugins/codec/libsdl_image_plugin.dll", "build/x86/plugins/codec/libspdif_plugin.dll", "build/x86/plugins/codec/libspeex_plugin.dll", "build/x86/plugins/codec/libspudec_plugin.dll", "build/x86/plugins/codec/libstl_plugin.dll", "build/x86/plugins/codec/libsubsdec_plugin.dll", "build/x86/plugins/codec/libsubstx3g_plugin.dll", "build/x86/plugins/codec/libsubsusf_plugin.dll", "build/x86/plugins/codec/libsvcdsub_plugin.dll", "build/x86/plugins/codec/libt140_plugin.dll", "build/x86/plugins/codec/libtextst_plugin.dll", "build/x86/plugins/codec/libtheora_plugin.dll", "build/x86/plugins/codec/libttml_plugin.dll", "build/x86/plugins/codec/libtwolame_plugin.dll", "build/x86/plugins/codec/libuleaddvaudio_plugin.dll", "build/x86/plugins/codec/libvorbis_plugin.dll", "build/x86/plugins/codec/libvpx_plugin.dll", "build/x86/plugins/codec/libwebvtt_plugin.dll", "build/x86/plugins/codec/libx26410b_plugin.dll", "build/x86/plugins/codec/libx264_plugin.dll", "build/x86/plugins/codec/libx265_plugin.dll", "build/x86/plugins/codec/libzvbi_plugin.dll", "build/x86/plugins/control/libdummy_plugin.dll", "build/x86/plugins/control/libgestures_plugin.dll", "build/x86/plugins/control/libhotkeys_plugin.dll", "build/x86/plugins/control/libnetsync_plugin.dll", "build/x86/plugins/control/libntservice_plugin.dll", "build/x86/plugins/control/liboldrc_plugin.dll", "build/x86/plugins/control/libwin_hotkeys_plugin.dll", "build/x86/plugins/control/libwin_msg_plugin.dll", "build/x86/plugins/d3d11/libdirect3d11_filters_plugin.dll", "build/x86/plugins/d3d9/libdirect3d9_filters_plugin.dll", "build/x86/plugins/demux/libadaptive_plugin.dll", "build/x86/plugins/demux/libaiff_plugin.dll", "build/x86/plugins/demux/libasf_plugin.dll", "build/x86/plugins/demux/libau_plugin.dll", "build/x86/plugins/demux/libavi_plugin.dll", "build/x86/plugins/demux/libcaf_plugin.dll", "build/x86/plugins/demux/libdemux_cdg_plugin.dll", "build/x86/plugins/demux/libdemux_chromecast_plugin.dll", "build/x86/plugins/demux/libdemux_stl_plugin.dll", "build/x86/plugins/demux/libdemuxdump_plugin.dll", "build/x86/plugins/demux/libdiracsys_plugin.dll", "build/x86/plugins/demux/libdirectory_demux_plugin.dll", "build/x86/plugins/demux/libes_plugin.dll", "build/x86/plugins/demux/libflacsys_plugin.dll", "build/x86/plugins/demux/libgme_plugin.dll", "build/x86/plugins/demux/libh26x_plugin.dll", "build/x86/plugins/demux/libimage_plugin.dll", "build/x86/plugins/demux/libmjpeg_plugin.dll", "build/x86/plugins/demux/libmkv_plugin.dll", "build/x86/plugins/demux/libmod_plugin.dll", "build/x86/plugins/demux/libmp4_plugin.dll", "build/x86/plugins/demux/libmpc_plugin.dll", "build/x86/plugins/demux/libmpgv_plugin.dll", "build/x86/plugins/demux/libnoseek_plugin.dll", "build/x86/plugins/demux/libnsc_plugin.dll", "build/x86/plugins/demux/libnsv_plugin.dll", "build/x86/plugins/demux/libnuv_plugin.dll", "build/x86/plugins/demux/libogg_plugin.dll", "build/x86/plugins/demux/libplaylist_plugin.dll", "build/x86/plugins/demux/libps_plugin.dll", "build/x86/plugins/demux/libpva_plugin.dll", "build/x86/plugins/demux/librawaud_plugin.dll", "build/x86/plugins/demux/librawdv_plugin.dll", "build/x86/plugins/demux/librawvid_plugin.dll", "build/x86/plugins/demux/libreal_plugin.dll", "build/x86/plugins/demux/libsid_plugin.dll", "build/x86/plugins/demux/libsmf_plugin.dll", "build/x86/plugins/demux/libsubtitle_plugin.dll", "build/x86/plugins/demux/libts_plugin.dll", "build/x86/plugins/demux/libtta_plugin.dll", "build/x86/plugins/demux/libty_plugin.dll", "build/x86/plugins/demux/libvc1_plugin.dll", "build/x86/plugins/demux/libvobsub_plugin.dll", "build/x86/plugins/demux/libvoc_plugin.dll", "build/x86/plugins/demux/libwav_plugin.dll", "build/x86/plugins/demux/libxa_plugin.dll", "build/x86/plugins/gui/libqt_plugin.dll", "build/x86/plugins/gui/libskins2_plugin.dll", "build/x86/plugins/keystore/libfile_keystore_plugin.dll", "build/x86/plugins/keystore/libmemory_keystore_plugin.dll", "build/x86/plugins/logger/libconsole_logger_plugin.dll", "build/x86/plugins/logger/libfile_logger_plugin.dll", "build/x86/plugins/lua/liblua_plugin.dll", "build/x86/plugins/meta_engine/libfolder_plugin.dll", "build/x86/plugins/meta_engine/libtaglib_plugin.dll", "build/x86/plugins/misc/libaddonsfsstorage_plugin.dll", "build/x86/plugins/misc/libaddonsvorepository_plugin.dll", "build/x86/plugins/misc/libaudioscrobbler_plugin.dll", "build/x86/plugins/misc/libexport_plugin.dll", "build/x86/plugins/misc/libfingerprinter_plugin.dll", "build/x86/plugins/misc/libgnutls_plugin.dll", "build/x86/plugins/misc/liblogger_plugin.dll", "build/x86/plugins/misc/libstats_plugin.dll", "build/x86/plugins/misc/libvod_rtsp_plugin.dll", "build/x86/plugins/misc/libxml_plugin.dll", "build/x86/plugins/mux/libmux_asf_plugin.dll", "build/x86/plugins/mux/libmux_avi_plugin.dll", "build/x86/plugins/mux/libmux_dummy_plugin.dll", "build/x86/plugins/mux/libmux_mp4_plugin.dll", "build/x86/plugins/mux/libmux_mpjpeg_plugin.dll", "build/x86/plugins/mux/libmux_ogg_plugin.dll", "build/x86/plugins/mux/libmux_ps_plugin.dll", "build/x86/plugins/mux/libmux_ts_plugin.dll", "build/x86/plugins/mux/libmux_wav_plugin.dll", "build/x86/plugins/packetizer/libpacketizer_a52_plugin.dll", "build/x86/plugins/packetizer/libpacketizer_av1_plugin.dll", "build/x86/plugins/packetizer/libpacketizer_copy_plugin.dll", "build/x86/plugins/packetizer/libpacketizer_dirac_plugin.dll", "build/x86/plugins/packetizer/libpacketizer_dts_plugin.dll", "build/x86/plugins/packetizer/libpacketizer_flac_plugin.dll", "build/x86/plugins/packetizer/libpacketizer_h264_plugin.dll", "build/x86/plugins/packetizer/libpacketizer_hevc_plugin.dll", "build/x86/plugins/packetizer/libpacketizer_mlp_plugin.dll", "build/x86/plugins/packetizer/libpacketizer_mpeg4audio_plugin.dll", "build/x86/plugins/packetizer/libpacketizer_mpeg4video_plugin.dll", "build/x86/plugins/packetizer/libpacketizer_mpegaudio_plugin.dll", "build/x86/plugins/packetizer/libpacketizer_mpegvideo_plugin.dll", "build/x86/plugins/packetizer/libpacketizer_vc1_plugin.dll", "build/x86/plugins/services_discovery/libmediadirs_plugin.dll", "build/x86/plugins/services_discovery/libmicrodns_plugin.dll", "build/x86/plugins/services_discovery/libpodcast_plugin.dll", "build/x86/plugins/services_discovery/libsap_plugin.dll", "build/x86/plugins/services_discovery/libupnp_plugin.dll", "build/x86/plugins/services_discovery/libwindrive_plugin.dll", "build/x86/plugins/spu/libaudiobargraph_v_plugin.dll", "build/x86/plugins/spu/liblogo_plugin.dll", "build/x86/plugins/spu/libmarq_plugin.dll", "build/x86/plugins/spu/libmosaic_plugin.dll", "build/x86/plugins/spu/libremoteosd_plugin.dll", "build/x86/plugins/spu/librss_plugin.dll", "build/x86/plugins/spu/libsubsdelay_plugin.dll", "build/x86/plugins/stream_extractor/libarchive_plugin.dll", "build/x86/plugins/stream_filter/libadf_plugin.dll", "build/x86/plugins/stream_filter/libaribcam_plugin.dll", "build/x86/plugins/stream_filter/libcache_block_plugin.dll", "build/x86/plugins/stream_filter/libcache_read_plugin.dll", "build/x86/plugins/stream_filter/libhds_plugin.dll", "build/x86/plugins/stream_filter/libinflate_plugin.dll", "build/x86/plugins/stream_filter/libprefetch_plugin.dll", "build/x86/plugins/stream_filter/librecord_plugin.dll", "build/x86/plugins/stream_filter/libskiptags_plugin.dll", "build/x86/plugins/stream_out/libstream_out_autodel_plugin.dll", "build/x86/plugins/stream_out/libstream_out_bridge_plugin.dll", "build/x86/plugins/stream_out/libstream_out_chromaprint_plugin.dll", "build/x86/plugins/stream_out/libstream_out_chromecast_plugin.dll", "build/x86/plugins/stream_out/libstream_out_cycle_plugin.dll", "build/x86/plugins/stream_out/libstream_out_delay_plugin.dll", "build/x86/plugins/stream_out/libstream_out_description_plugin.dll", "build/x86/plugins/stream_out/libstream_out_display_plugin.dll", "build/x86/plugins/stream_out/libstream_out_dummy_plugin.dll", "build/x86/plugins/stream_out/libstream_out_duplicate_plugin.dll", "build/x86/plugins/stream_out/libstream_out_es_plugin.dll", "build/x86/plugins/stream_out/libstream_out_gather_plugin.dll", "build/x86/plugins/stream_out/libstream_out_mosaic_bridge_plugin.dll", "build/x86/plugins/stream_out/libstream_out_record_plugin.dll", "build/x86/plugins/stream_out/libstream_out_rtp_plugin.dll", "build/x86/plugins/stream_out/libstream_out_setid_plugin.dll", "build/x86/plugins/stream_out/libstream_out_smem_plugin.dll", "build/x86/plugins/stream_out/libstream_out_standard_plugin.dll", "build/x86/plugins/stream_out/libstream_out_stats_plugin.dll", "build/x86/plugins/stream_out/libstream_out_transcode_plugin.dll", "build/x86/plugins/text_renderer/libfreetype_plugin.dll", "build/x86/plugins/text_renderer/libsapi_plugin.dll", "build/x86/plugins/text_renderer/libtdummy_plugin.dll", "build/x86/plugins/video_chroma/libchain_plugin.dll", "build/x86/plugins/video_chroma/libgrey_yuv_plugin.dll", "build/x86/plugins/video_chroma/libi420_10_p010_plugin.dll", "build/x86/plugins/video_chroma/libi420_nv12_plugin.dll", "build/x86/plugins/video_chroma/libi420_rgb_mmx_plugin.dll", "build/x86/plugins/video_chroma/libi420_rgb_plugin.dll", "build/x86/plugins/video_chroma/libi420_rgb_sse2_plugin.dll", "build/x86/plugins/video_chroma/libi420_yuy2_mmx_plugin.dll", "build/x86/plugins/video_chroma/libi420_yuy2_plugin.dll", "build/x86/plugins/video_chroma/libi420_yuy2_sse2_plugin.dll", "build/x86/plugins/video_chroma/libi422_i420_plugin.dll", "build/x86/plugins/video_chroma/libi422_yuy2_mmx_plugin.dll", "build/x86/plugins/video_chroma/libi422_yuy2_plugin.dll", "build/x86/plugins/video_chroma/libi422_yuy2_sse2_plugin.dll", "build/x86/plugins/video_chroma/librv32_plugin.dll", "build/x86/plugins/video_chroma/libswscale_plugin.dll", "build/x86/plugins/video_chroma/libyuvp_plugin.dll", "build/x86/plugins/video_chroma/libyuy2_i420_plugin.dll", "build/x86/plugins/video_chroma/libyuy2_i422_plugin.dll", "build/x86/plugins/video_filter/libadjust_plugin.dll", "build/x86/plugins/video_filter/libalphamask_plugin.dll", "build/x86/plugins/video_filter/libanaglyph_plugin.dll", "build/x86/plugins/video_filter/libantiflicker_plugin.dll", "build/x86/plugins/video_filter/libball_plugin.dll", "build/x86/plugins/video_filter/libblend_plugin.dll", "build/x86/plugins/video_filter/libblendbench_plugin.dll", "build/x86/plugins/video_filter/libbluescreen_plugin.dll", "build/x86/plugins/video_filter/libcanvas_plugin.dll", "build/x86/plugins/video_filter/libcolorthres_plugin.dll", "build/x86/plugins/video_filter/libcroppadd_plugin.dll", "build/x86/plugins/video_filter/libdeinterlace_plugin.dll", "build/x86/plugins/video_filter/libedgedetection_plugin.dll", "build/x86/plugins/video_filter/liberase_plugin.dll", "build/x86/plugins/video_filter/libextract_plugin.dll", "build/x86/plugins/video_filter/libfps_plugin.dll", "build/x86/plugins/video_filter/libfreeze_plugin.dll", "build/x86/plugins/video_filter/libgaussianblur_plugin.dll", "build/x86/plugins/video_filter/libgradfun_plugin.dll", "build/x86/plugins/video_filter/libgradient_plugin.dll", "build/x86/plugins/video_filter/libgrain_plugin.dll", "build/x86/plugins/video_filter/libhqdn3d_plugin.dll", "build/x86/plugins/video_filter/libinvert_plugin.dll", "build/x86/plugins/video_filter/libmagnify_plugin.dll", "build/x86/plugins/video_filter/libmirror_plugin.dll", "build/x86/plugins/video_filter/libmotionblur_plugin.dll", "build/x86/plugins/video_filter/libmotiondetect_plugin.dll", "build/x86/plugins/video_filter/liboldmovie_plugin.dll", "build/x86/plugins/video_filter/libposterize_plugin.dll", "build/x86/plugins/video_filter/libpostproc_plugin.dll", "build/x86/plugins/video_filter/libpsychedelic_plugin.dll", "build/x86/plugins/video_filter/libpuzzle_plugin.dll", "build/x86/plugins/video_filter/libripple_plugin.dll", "build/x86/plugins/video_filter/librotate_plugin.dll", "build/x86/plugins/video_filter/libscale_plugin.dll", "build/x86/plugins/video_filter/libscene_plugin.dll", "build/x86/plugins/video_filter/libsepia_plugin.dll", "build/x86/plugins/video_filter/libsharpen_plugin.dll", "build/x86/plugins/video_filter/libtransform_plugin.dll", "build/x86/plugins/video_filter/libvhs_plugin.dll", "build/x86/plugins/video_filter/libwave_plugin.dll", "build/x86/plugins/video_output/libcaca_plugin.dll", "build/x86/plugins/video_output/libdirect3d11_plugin.dll", "build/x86/plugins/video_output/libdirect3d9_plugin.dll", "build/x86/plugins/video_output/libdirectdraw_plugin.dll", "build/x86/plugins/video_output/libdrawable_plugin.dll", "build/x86/plugins/video_output/libflaschen_plugin.dll", "build/x86/plugins/video_output/libgl_plugin.dll", "build/x86/plugins/video_output/libglwin32_plugin.dll", "build/x86/plugins/video_output/libvdummy_plugin.dll", "build/x86/plugins/video_output/libvmem_plugin.dll", "build/x86/plugins/video_output/libwgl_plugin.dll", "build/x86/plugins/video_output/libwingdi_plugin.dll", "build/x86/plugins/video_output/libwinhibit_plugin.dll", "build/x86/plugins/video_output/libyuv_plugin.dll", "build/x86/plugins/video_splitter/libclone_plugin.dll", "build/x86/plugins/video_splitter/libpanoramix_plugin.dll", "build/x86/plugins/video_splitter/libwall_plugin.dll", "build/x86/plugins/visualization/libglspectrum_plugin.dll", "build/x86/plugins/visualization/libgoom_plugin.dll", "build/x86/plugins/visualization/libprojectm_plugin.dll", "build/x86/plugins/visualization/libvisual_plugin.dll", "build/x86/vlc.lib", "build/x86/vlccore.lib", "icon.png", "videolan.libvlc.windows.3.0.18.nupkg.sha512", "videolan.libvlc.windows.nuspec"]}}, "projectFileDependencyGroups": {"net9.0-windows7.0": ["LibVLCSharp >= 3.8.0", "LibVLCSharp.WPF >= 3.8.0", "VideoLAN.LibVLC.Windows >= 3.0.18"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\MediaPlayerApp\\MediaPlayerApp.csproj", "projectName": "MediaPlayerApp", "projectPath": "C:\\Users\\<USER>\\MediaPlayerApp\\MediaPlayerApp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\MediaPlayerApp\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"LibVLCSharp": {"target": "Package", "version": "[3.8.0, )"}, "LibVLCSharp.WPF": {"target": "Package", "version": "[3.8.0, )"}, "VideoLAN.LibVLC.Windows": {"target": "Package", "version": "[3.0.18, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}