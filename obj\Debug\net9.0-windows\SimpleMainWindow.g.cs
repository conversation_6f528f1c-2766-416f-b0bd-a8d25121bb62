﻿#pragma checksum "..\..\..\SimpleMainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "1D1DC22AC36FB015273669F81567AEF635997C07"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MediaPlayerApp {
    
    
    /// <summary>
    /// SimpleMainWindow
    /// </summary>
    public partial class SimpleMainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 54 "..\..\..\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock fileInfoText;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.MediaElement mediaPlayer;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid welcomeScreen;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid videoOverlay;
        
        #line default
        #line hidden
        
        
        #line 164 "..\..\..\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button overlayPlayButton;
        
        #line default
        #line hidden
        
        
        #line 187 "..\..\..\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock currentTime;
        
        #line default
        #line hidden
        
        
        #line 193 "..\..\..\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border progressBackground;
        
        #line default
        #line hidden
        
        
        #line 195 "..\..\..\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider progressSlider;
        
        #line default
        #line hidden
        
        
        #line 199 "..\..\..\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock totalTime;
        
        #line default
        #line hidden
        
        
        #line 205 "..\..\..\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider volumeSlider;
        
        #line default
        #line hidden
        
        
        #line 207 "..\..\..\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock volumeText;
        
        #line default
        #line hidden
        
        
        #line 246 "..\..\..\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button playButton;
        
        #line default
        #line hidden
        
        
        #line 293 "..\..\..\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock statusText;
        
        #line default
        #line hidden
        
        
        #line 296 "..\..\..\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock playlistInfo;
        
        #line default
        #line hidden
        
        
        #line 302 "..\..\..\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock qualityText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.4.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MediaPlayerApp;component/simplemainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\SimpleMainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.4.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 30 "..\..\..\SimpleMainWindow.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.TitleBar_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 2:
            this.fileInfoText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            
            #line 61 "..\..\..\SimpleMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Refresh_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 64 "..\..\..\SimpleMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Settings_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 67 "..\..\..\SimpleMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Minimize_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 70 "..\..\..\SimpleMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Maximize_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 73 "..\..\..\SimpleMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Close_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 87 "..\..\..\SimpleMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.OpenFile_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 90 "..\..\..\SimpleMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Playlist_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 93 "..\..\..\SimpleMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Equalizer_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 96 "..\..\..\SimpleMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Theme_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 99 "..\..\..\SimpleMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Fullscreen_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.mediaPlayer = ((System.Windows.Controls.MediaElement)(target));
            return;
            case 14:
            this.welcomeScreen = ((System.Windows.Controls.Grid)(target));
            return;
            case 15:
            this.videoOverlay = ((System.Windows.Controls.Grid)(target));
            return;
            case 16:
            
            #line 163 "..\..\..\SimpleMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Previous_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.overlayPlayButton = ((System.Windows.Controls.Button)(target));
            
            #line 166 "..\..\..\SimpleMainWindow.xaml"
            this.overlayPlayButton.Click += new System.Windows.RoutedEventHandler(this.PlayPause_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            
            #line 168 "..\..\..\SimpleMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Next_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.currentTime = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.progressBackground = ((System.Windows.Controls.Border)(target));
            return;
            case 21:
            this.progressSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 22:
            this.totalTime = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.volumeSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 24:
            this.volumeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 25:
            
            #line 229 "..\..\..\SimpleMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Shuffle_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            
            #line 232 "..\..\..\SimpleMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Repeat_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            
            #line 235 "..\..\..\SimpleMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Favorite_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            
            #line 243 "..\..\..\SimpleMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Previous_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            this.playButton = ((System.Windows.Controls.Button)(target));
            
            #line 248 "..\..\..\SimpleMainWindow.xaml"
            this.playButton.Click += new System.Windows.RoutedEventHandler(this.PlayPause_Click);
            
            #line default
            #line hidden
            return;
            case 30:
            
            #line 255 "..\..\..\SimpleMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Stop_Click);
            
            #line default
            #line hidden
            return;
            case 31:
            
            #line 259 "..\..\..\SimpleMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Next_Click);
            
            #line default
            #line hidden
            return;
            case 32:
            
            #line 267 "..\..\..\SimpleMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Subtitle_Click);
            
            #line default
            #line hidden
            return;
            case 33:
            
            #line 270 "..\..\..\SimpleMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Filter_Click);
            
            #line default
            #line hidden
            return;
            case 34:
            
            #line 273 "..\..\..\SimpleMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Cast_Click);
            
            #line default
            #line hidden
            return;
            case 35:
            this.statusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 36:
            this.playlistInfo = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 37:
            this.qualityText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

