﻿<Window x:Class="MediaPlayerApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:MediaPlayerApp"
        mc:Ignorable="d"
        Title="مشغل الوسائط - Media Player" Height="600" Width="900"
        MinHeight="400" MinWidth="600">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Menu Bar -->
        <Menu Grid.Row="0" Background="LightGray">
            <MenuItem Header="ملف">
                <MenuItem Header="فتح ملف..." Click="OpenFile_Click"/>
                <Separator/>
                <MenuItem Header="خروج" Click="Exit_Click"/>
            </MenuItem>
            <MenuItem Header="تشغيل">
                <MenuItem Header="تشغيل/إيقاف مؤقت" Click="PlayPause_Click"/>
                <MenuItem Header="إيقاف" Click="Stop_Click"/>
            </MenuItem>
        </Menu>

        <!-- Video Display Area -->
        <Border Grid.Row="1" Background="Black" Margin="5">
            <MediaElement x:Name="mediaPlayer"
                         LoadedBehavior="Manual"
                         UnloadedBehavior="Stop"
                         Stretch="Uniform"
                         MediaOpened="MediaPlayer_MediaOpened"
                         MediaEnded="MediaPlayer_MediaEnded"/>
        </Border>

        <!-- Progress Bar -->
        <Grid Grid.Row="2" Margin="10,5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock x:Name="currentTimeText" Grid.Column="0"
                      Text="00:00" VerticalAlignment="Center" Margin="0,0,10,0"/>

            <Slider x:Name="progressSlider" Grid.Column="1"
                   Minimum="0" Maximum="100"
                   ValueChanged="ProgressSlider_ValueChanged"
                   Thumb.DragStarted="ProgressSlider_DragStarted"
                   Thumb.DragCompleted="ProgressSlider_DragCompleted"/>

            <TextBlock x:Name="totalTimeText" Grid.Column="2"
                      Text="00:00" VerticalAlignment="Center" Margin="10,0,0,0"/>
        </Grid>

        <!-- Control Panel -->
        <Grid Grid.Row="3" Background="LightGray" Height="60">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Control Buttons -->
            <StackPanel Grid.Column="1" Orientation="Horizontal"
                       HorizontalAlignment="Center" VerticalAlignment="Center">

                <Button x:Name="openButton" Content="📁"
                       Width="40" Height="40" Margin="5"
                       Click="OpenFile_Click" ToolTip="فتح ملف"/>

                <Button x:Name="playPauseButton" Content="▶️"
                       Width="50" Height="40" Margin="5"
                       Click="PlayPause_Click" ToolTip="تشغيل/إيقاف مؤقت"/>

                <Button x:Name="stopButton" Content="⏹️"
                       Width="40" Height="40" Margin="5"
                       Click="Stop_Click" ToolTip="إيقاف"/>

            </StackPanel>

            <!-- Volume Control -->
            <StackPanel Grid.Column="2" Orientation="Horizontal"
                       HorizontalAlignment="Right" VerticalAlignment="Center" Margin="10,0">

                <TextBlock Text="🔊" VerticalAlignment="Center" Margin="0,0,5,0"/>

                <Slider x:Name="volumeSlider"
                       Width="100" Minimum="0" Maximum="1" Value="0.5"
                       ValueChanged="VolumeSlider_ValueChanged"
                       VerticalAlignment="Center"/>

                <TextBlock x:Name="volumeText" Text="50%"
                          VerticalAlignment="Center" Margin="5,0,0,0" Width="30"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window>
