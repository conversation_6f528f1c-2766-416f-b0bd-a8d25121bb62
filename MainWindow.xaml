﻿<Window x:Class="MediaPlayerApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:MediaPlayerApp"
        mc:Ignorable="d"
        Title="🎬 مشغل الوسائط المتطور 2025 - Advanced Media Player"
        Height="700" Width="1200"
        MinHeight="500" MinWidth="800"
        WindowStartupLocation="CenterScreen"
        Background="#FF1E1E1E"
        WindowStyle="SingleBorderWindow"
        ResizeMode="CanResize">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Modern Title Bar -->
        <Border Grid.Row="0" Background="#FF2D2D30" Height="50">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- App Icon and Title -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="15,0">
                    <TextBlock Text="🎬" FontSize="20" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Text="مشغل الوسائط المتطور 2025"
                              FontSize="14" FontWeight="Bold"
                              Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>

                <!-- File Info -->
                <TextBlock x:Name="fileInfoText" Grid.Column="1"
                          Text="جاهز للتشغيل - Ready to Play"
                          FontSize="12" Foreground="#FFCCCCCC"
                          VerticalAlignment="Center" HorizontalAlignment="Center"/>

                <!-- Modern Menu Buttons -->
                <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center" Margin="15,0">
                    <Button Content="📁" Width="35" Height="35" Margin="5,0"
                           Background="#FF404040" Foreground="White" BorderBrush="Transparent"
                           Click="OpenFile_Click" ToolTip="فتح ملف - Open File"
                           Style="{DynamicResource ModernButtonStyle}"/>
                    <Button Content="⚙️" Width="35" Height="35" Margin="5,0"
                           Background="#FF404040" Foreground="White" BorderBrush="Transparent"
                           Click="Settings_Click" ToolTip="الإعدادات - Settings"
                           Style="{DynamicResource ModernButtonStyle}"/>
                    <Button Content="❌" Width="35" Height="35" Margin="5,0"
                           Background="#FFDC3545" Foreground="White" BorderBrush="Transparent"
                           Click="Exit_Click" ToolTip="إغلاق - Close"
                           Style="{DynamicResource ModernButtonStyle}"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Modern Video Display Area -->
        <Border Grid.Row="1" Background="#FF000000" Margin="10" CornerRadius="10">
            <Grid>
                <MediaElement x:Name="mediaPlayer"
                             LoadedBehavior="Manual"
                             UnloadedBehavior="Stop"
                             Stretch="Uniform"
                             MediaOpened="MediaPlayer_MediaOpened"
                             MediaEnded="MediaPlayer_MediaEnded"/>

                <!-- Welcome Screen -->
                <Border x:Name="welcomeScreen" Background="#FF1E1E1E" CornerRadius="10">
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                        <TextBlock Text="🎬" FontSize="80" HorizontalAlignment="Center"
                                  Foreground="#FF007ACC" Margin="0,0,0,20"/>
                        <TextBlock Text="مشغل الوسائط المتطور 2025"
                                  FontSize="24" FontWeight="Bold"
                                  Foreground="White" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="Advanced Media Player 2025"
                                  FontSize="16"
                                  Foreground="#FFCCCCCC" HorizontalAlignment="Center" Margin="0,0,0,20"/>
                        <TextBlock Text="اسحب ملف هنا أو اضغط فتح ملف للبدء"
                                  FontSize="14"
                                  Foreground="#FF888888" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                        <TextBlock Text="Drag a file here or click Open File to start"
                                  FontSize="12"
                                  Foreground="#FF666666" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- Video Controls Overlay -->
                <Grid x:Name="videoControlsOverlay" Visibility="Collapsed">
                    <Border Background="#80000000" VerticalAlignment="Bottom" Height="60">
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                            <Button Content="⏮️" Width="40" Height="40" Margin="5"
                                   Background="Transparent" Foreground="White" BorderBrush="Transparent"
                                   Click="Previous_Click" ToolTip="السابق - Previous"/>
                            <Button x:Name="overlayPlayPauseButton" Content="▶️" Width="50" Height="50" Margin="5"
                                   Background="#FF007ACC" Foreground="White" BorderBrush="Transparent"
                                   Click="PlayPause_Click" ToolTip="تشغيل/إيقاف - Play/Pause"/>
                            <Button Content="⏭️" Width="40" Height="40" Margin="5"
                                   Background="Transparent" Foreground="White" BorderBrush="Transparent"
                                   Click="Next_Click" ToolTip="التالي - Next"/>
                        </StackPanel>
                    </Border>
                </Grid>
            </Grid>
        </Border>

        <!-- Modern Progress Bar -->
        <Border Grid.Row="2" Background="#FF2D2D30" Margin="10,0" CornerRadius="5" Padding="15,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock x:Name="currentTimeText" Grid.Column="0"
                          Text="00:00" VerticalAlignment="Center" Margin="0,0,15,0"
                          Foreground="White" FontFamily="Consolas" FontSize="12"/>

                <Slider x:Name="progressSlider" Grid.Column="1"
                       Minimum="0" Maximum="100" Height="6"
                       ValueChanged="ProgressSlider_ValueChanged"
                       Thumb.DragStarted="ProgressSlider_DragStarted"
                       Thumb.DragCompleted="ProgressSlider_DragCompleted"
                       Background="#FF404040" Foreground="#FF007ACC"/>

                <TextBlock x:Name="totalTimeText" Grid.Column="2"
                          Text="00:00" VerticalAlignment="Center" Margin="15,0,0,0"
                          Foreground="White" FontFamily="Consolas" FontSize="12"/>
            </Grid>
        </Border>

        <!-- Modern Control Panel -->
        <Border Grid.Row="3" Background="#FF2D2D30" Height="80" Margin="10,0,10,10" CornerRadius="5">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Left Side Controls -->
                <StackPanel Grid.Column="0" Orientation="Horizontal"
                           HorizontalAlignment="Left" VerticalAlignment="Center" Margin="20,0">
                    <Button Content="🔀" Width="40" Height="40" Margin="5"
                           Background="#FF404040" Foreground="White" BorderBrush="Transparent"
                           Click="Shuffle_Click" ToolTip="خلط - Shuffle"/>
                    <Button Content="🔁" Width="40" Height="40" Margin="5"
                           Background="#FF404040" Foreground="White" BorderBrush="Transparent"
                           Click="Repeat_Click" ToolTip="تكرار - Repeat"/>
                    <Button Content="📋" Width="40" Height="40" Margin="5"
                           Background="#FF404040" Foreground="White" BorderBrush="Transparent"
                           Click="Playlist_Click" ToolTip="قائمة التشغيل - Playlist"/>
                </StackPanel>

                <!-- Center Control Buttons -->
                <StackPanel Grid.Column="1" Orientation="Horizontal"
                           HorizontalAlignment="Center" VerticalAlignment="Center">

                    <Button Content="⏮️" Width="45" Height="45" Margin="5"
                           Background="#FF404040" Foreground="White" BorderBrush="Transparent"
                           Click="Previous_Click" ToolTip="السابق - Previous"/>

                    <Button x:Name="playPauseButton" Content="▶️"
                           Width="60" Height="60" Margin="10"
                           Background="#FF007ACC" Foreground="White" BorderBrush="Transparent"
                           Click="PlayPause_Click" ToolTip="تشغيل/إيقاف مؤقت - Play/Pause"
                           FontSize="20"/>

                    <Button Content="⏹️" Width="45" Height="45" Margin="5"
                           Background="#FF404040" Foreground="White" BorderBrush="Transparent"
                           Click="Stop_Click" ToolTip="إيقاف - Stop"/>

                    <Button Content="⏭️" Width="45" Height="45" Margin="5"
                           Background="#FF404040" Foreground="White" BorderBrush="Transparent"
                           Click="Next_Click" ToolTip="التالي - Next"/>

                </StackPanel>

                <!-- Right Side Controls -->
                <StackPanel Grid.Column="2" Orientation="Horizontal"
                           HorizontalAlignment="Right" VerticalAlignment="Center" Margin="20,0">

                    <TextBlock Text="🔊" VerticalAlignment="Center" Margin="0,0,10,0"
                              Foreground="White" FontSize="16"/>

                    <Slider x:Name="volumeSlider"
                           Width="120" Minimum="0" Maximum="1" Value="0.5"
                           ValueChanged="VolumeSlider_ValueChanged"
                           VerticalAlignment="Center" Height="6"
                           Background="#FF404040" Foreground="#FF007ACC"/>

                    <TextBlock x:Name="volumeText" Text="50%"
                              VerticalAlignment="Center" Margin="10,0,0,0" Width="35"
                              Foreground="White" FontFamily="Consolas" FontSize="12"/>

                    <Button Content="🎵" Width="40" Height="40" Margin="10,0,0,0"
                           Background="#FF404040" Foreground="White" BorderBrush="Transparent"
                           Click="Equalizer_Click" ToolTip="المعادل - Equalizer"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
