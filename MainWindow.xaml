﻿<Window x:Class="MediaPlayerApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:MediaPlayerApp"
        mc:Ignorable="d"
        Title="🎬 مشغل الوسائط المتطور 2025 - Advanced Media Player"
        Height="600" Width="900"
        MinHeight="400" MinWidth="600"
        WindowStartupLocation="CenterScreen"
        Background="#FF1E1E1E">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Simple Title Bar -->
        <Border Grid.Row="0" Background="#FF2D2D30" Height="40">
            <Grid>
                <TextBlock Text="🎬 مشغل الوسائط المتطور 2025 - Advanced Media Player"
                          FontSize="14" FontWeight="Bold"
                          Foreground="White" VerticalAlignment="Center"
                          HorizontalAlignment="Center"/>
            </Grid>
        </Border>

        <!-- Video Display Area -->
        <Border Grid.Row="1" Background="Black" Margin="10" CornerRadius="5">
            <Grid>
                <MediaElement x:Name="mediaPlayer"
                             LoadedBehavior="Manual"
                             UnloadedBehavior="Stop"
                             Stretch="Uniform"
                             MediaOpened="MediaPlayer_MediaOpened"
                             MediaEnded="MediaPlayer_MediaEnded"/>

                <!-- Welcome Screen -->
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center"
                           x:Name="welcomeScreen">
                    <TextBlock Text="🎬" FontSize="60" HorizontalAlignment="Center"
                              Foreground="#FF007ACC" Margin="0,0,0,15"/>
                    <TextBlock Text="مشغل الوسائط المتطور 2025"
                              FontSize="18" FontWeight="Bold"
                              Foreground="White" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                    <TextBlock Text="Advanced Media Player 2025"
                              FontSize="14"
                              Foreground="#FFCCCCCC" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                    <TextBlock Text="اضغط فتح ملف للبدء - Click Open File to start"
                              FontSize="12"
                              Foreground="#FF888888" HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Progress Bar -->
        <Border Grid.Row="2" Background="#FF2D2D30" Margin="10,5" CornerRadius="3" Padding="10,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock x:Name="currentTimeText" Grid.Column="0"
                          Text="00:00" VerticalAlignment="Center" Margin="0,0,10,0"
                          Foreground="White" FontSize="11"/>

                <Slider x:Name="progressSlider" Grid.Column="1"
                       Minimum="0" Maximum="100"
                       ValueChanged="ProgressSlider_ValueChanged"
                       Thumb.DragStarted="ProgressSlider_DragStarted"
                       Thumb.DragCompleted="ProgressSlider_DragCompleted"/>

                <TextBlock x:Name="totalTimeText" Grid.Column="2"
                          Text="00:00" VerticalAlignment="Center" Margin="10,0,0,0"
                          Foreground="White" FontSize="11"/>
            </Grid>
        </Border>

        <!-- Control Panel -->
        <Border Grid.Row="3" Background="#FF2D2D30" Height="70" Margin="10,0,10,10" CornerRadius="3">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Open File Button -->
                <Button Grid.Column="0" Content="📁 فتح ملف"
                       Width="80" Height="35" Margin="15,0"
                       Background="#FF404040" Foreground="White" BorderBrush="Transparent"
                       Click="OpenFile_Click" ToolTip="فتح ملف - Open File"/>

                <!-- Center Control Buttons -->
                <StackPanel Grid.Column="1" Orientation="Horizontal"
                           HorizontalAlignment="Center" VerticalAlignment="Center">

                    <Button Content="⏮️" Width="40" Height="40" Margin="5"
                           Background="#FF404040" Foreground="White" BorderBrush="Transparent"
                           Click="Previous_Click" ToolTip="السابق - Previous"/>

                    <Button x:Name="playPauseButton" Content="▶️"
                           Width="50" Height="50" Margin="8"
                           Background="#FF007ACC" Foreground="White" BorderBrush="Transparent"
                           Click="PlayPause_Click" ToolTip="تشغيل/إيقاف مؤقت - Play/Pause"
                           FontSize="16"/>

                    <Button Content="⏹️" Width="40" Height="40" Margin="5"
                           Background="#FF404040" Foreground="White" BorderBrush="Transparent"
                           Click="Stop_Click" ToolTip="إيقاف - Stop"/>

                    <Button Content="⏭️" Width="40" Height="40" Margin="5"
                           Background="#FF404040" Foreground="White" BorderBrush="Transparent"
                           Click="Next_Click" ToolTip="التالي - Next"/>

                </StackPanel>

                <!-- Volume Control -->
                <StackPanel Grid.Column="2" Orientation="Horizontal"
                           HorizontalAlignment="Right" VerticalAlignment="Center" Margin="15,0">

                    <TextBlock Text="🔊" VerticalAlignment="Center" Margin="0,0,8,0"
                              Foreground="White" FontSize="14"/>

                    <Slider x:Name="volumeSlider"
                           Width="80" Minimum="0" Maximum="1" Value="0.5"
                           ValueChanged="VolumeSlider_ValueChanged"
                           VerticalAlignment="Center"/>

                    <TextBlock x:Name="volumeText" Text="50%"
                              VerticalAlignment="Center" Margin="8,0,0,0" Width="30"
                              Foreground="White" FontSize="10"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
