root { 
    display: block;
}
body{
}
#mainContainer{
	text-align: center;
	width: 800px;
}
#controlContainer{
	width: 800px;
}
#libraryContainer{
	width: 800px;
	margin-top: 2px;
}
#libraryTree{
	height: 300px;
	overflow: auto;
	white-space: nowrap;
	text-align: left;
}
#viewContainer{
	width: 800px;
}
#mediaViewer{
	min-height: 500px;
	background-color:#222;
}
#player{
	top:0px;
	height: 500px;
	width: 500px;
	background-color:#222;
}
#seekSlider{
	width: 98%;
	margin-left:5px;
}
#volumeSlider{
	width: 100px;
	display: inline-block;
}
#currentVolume{
	display: inline-block;
}
#mediaTitle{
	position: absolute;
	top: 0px;
	left: 10px;
	width: 600px;
	text-align: center;
	padding: 5px;
	overflow: auto;
}
#currentTime{
	margin-top:-40px;
	float: left;
	text-align: left;
}

#totalTime{
	margin-top:-40px;
	float: right;
	text-align: right;
}

#controlTable{
	position:relative;
	height: 150px;
}
#controlButtons{
	position: absolute;
	top: 80px;
	left: 10px;
	padding: 0;
	margin: 0;
	text-align: left;
}

.buttonszone{
	position:absolute;
	width: 20px;
	margin-left:-20px;
	vertical-align:top;
	padding:0px;
	font-size:0px;
	line-height:0px;
}

#buttonszone1 {
	top: 200px;
}

#buttonszone2 {
	top: 20px;
}

.buttonszone li{
	float:left;
	clear:left;
	font-size:0px;
}

.buttonszone li span{
	float:left
}

.buttonszone_active {
	margin-top: 0px;
	width: 120px;
	margin-left:-122px;
	font-size:10px;
	line-height:16px;
}

.buttonszone_active li {
	width:120px;
}


#volumesliderzone{
	position: absolute;
	top: 105px;
	left: 410px;
	width: 210px;
}

#volumeSlider{
	width: 208px;
}
#libraryTree ul li a {
	border:none;
}
#artszone{
	position: absolute;
	top: 0px;
	right: 10px;
	width: 150px;
	height: 150px;
	vertical-align: top;
}

#seekContainer{
	position:absolute;
	left: 5px;
	top: 55px;
	width: 79%;
	vertical-align: bottom;
}

.button48{
	width: 48px;
	height: 48px;
	margin: 5px 0px 5px 2px;
	background: none;
	border: none;
	display: inline-block;
	list-style: none;
	float:left;
	cursor: pointer;
	background-image: url("../images/buttons.png");
	background-repeat: no-repeat;
}
.button48:hover{
	filter: alpha(opacity:0.5);
	-ms-filter: "alpha(opacity=50)";
	-khtml-opacity: 0.50;
	-moz-opacity: 0.5;
	filter: alpha(opacity=50);
	opacity: 0.5;
}

.button{
	cursor: pointer;
	display: inline-block;
}
#buttonOpen{
	background-position: 0px 0px;
}
#buttonStop{
	background-position: -576px 0px;
}
.playing {
	background-position: -336px 0px;
}
.paused {
	background-position: -384px 0px;
}
#buttonPrev{
	background-position: -144px 0px;
}
#buttonNext{
	background-position: -288px 0px;
}
#buttonFull{
	background-position: -192px 0px;
}
#buttonSout{
	background-position: -624px 0px;
}
#buttonEQ{
	background-position: -48px 0px;
}
#window_browse ol{
	list-style-type: none;
}
#window_browse ol li{
	list-style-type: none;
	float: left;
	padding: 5px;
}
.system_icon{
	width:80px;
	text-align:center;
	vertical-align:top;
	display: inline-block;
	cursor: pointer;
	padding: 2px;
	border: 1px solid #823D0A;
	margin: 2px;
	height: 92px;
	background-color: #E1E1E1;
	overflow: hidden;
}
#window_create_stream table tr td{
	font-size: 11px;
}

#window_equalizer div div{
	text-align: center;
	font-size: 11px;
	padding: 0px;
}

#window_equalizer {
	height:80px !important ;
}

.eqBand{
	margin-bottom: 10px;
	margin-top: 10px;
	height: 400px;
	font-size: 1.5em;
}
.footer{
	margin-top: 30px;
	text-align: center;
	font-size: 11px;
}

div.centered{
	margin-left: auto;
	margin-right: auto;
}

.hidden{
	visibility: hidden;
	display: none;
}
