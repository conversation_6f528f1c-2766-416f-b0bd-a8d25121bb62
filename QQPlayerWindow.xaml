<Window x:Class="MediaPlayerApp.QQPlayerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="QQPlayer" 
        Height="800" Width="1400"
        MinHeight="500" MinWidth="800"
        WindowStartupLocation="CenterScreen"
        Background="Black"
        WindowStyle="None"
        AllowsTransparency="True"
        ResizeMode="CanResize">

    <Window.Resources>
        <!-- تعريف الألوان والأنماط -->
        <SolidColorBrush x:Key="PrimaryBlue" Color="#FF0078D4"/>
        <SolidColorBrush x:Key="DarkBackground" Color="#FF1A1A1A"/>
        <SolidColorBrush x:Key="ControlBackground" Color="#FF2D2D2D"/>
        <SolidColorBrush x:Key="HoverBlue" Color="#FF106EBE"/>
        
        <!-- نمط الأزرار -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="5" Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource HoverBlue}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- نمط شريط التقدم -->
        <Style x:Key="ModernSlider" TargetType="Slider">
            <Setter Property="Background" Value="#FF404040"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryBlue}"/>
            <Setter Property="Height" Value="6"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>
    </Window.Resources>

    <Grid>
        <!-- الخلفية الرئيسية -->
        <Grid.Background>
            <RadialGradientBrush>
                <GradientStop Color="#FF0A0A0A" Offset="0"/>
                <GradientStop Color="#FF1A1A2E" Offset="0.5"/>
                <GradientStop Color="#FF16213E" Offset="1"/>
            </RadialGradientBrush>
        </Grid.Background>

        <!-- شريط العنوان المخصص -->
        <Grid x:Name="TitleBar" Height="35" VerticalAlignment="Top" 
              Background="#FF1A1A1A" MouseLeftButtonDown="TitleBar_MouseLeftButtonDown">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- أيقونة وعنوان التطبيق -->
            <StackPanel Grid.Column="0" Orientation="Horizontal" Margin="10,0">
                <TextBlock Text="🎬" FontSize="16" VerticalAlignment="Center" Margin="0,0,5,0"/>
                <TextBlock Text="QQPlayer" Foreground="White" FontSize="14" 
                          VerticalAlignment="Center" FontWeight="Bold"/>
            </StackPanel>

            <!-- أزرار التحكم في النافذة -->
            <StackPanel Grid.Column="2" Orientation="Horizontal">
                <Button x:Name="MinimizeButton" Content="🗕" Width="45" Height="35" 
                        Style="{StaticResource ModernButton}" Click="MinimizeButton_Click"/>
                <Button x:Name="MaximizeButton" Content="🗖" Width="45" Height="35" 
                        Style="{StaticResource ModernButton}" Click="MaximizeButton_Click"/>
                <Button x:Name="CloseButton" Content="✕" Width="45" Height="35" 
                        Style="{StaticResource ModernButton}" Click="CloseButton_Click"
                        Background="#FFDC3545"/>
            </StackPanel>
        </Grid>

        <!-- المحتوى الرئيسي -->
        <Grid Margin="0,35,0,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- شريط القوائم -->
            <Grid Grid.Row="0" Height="40" Background="#FF2D2D2D">
                <StackPanel Orientation="Horizontal" Margin="15,0">
                    <Button x:Name="OpenFileBtn" Content="📁 فتح ملف" Style="{StaticResource ModernButton}" 
                            Click="OpenFile_Click" Margin="0,0,10,0"/>
                    <Button x:Name="OpenFolderBtn" Content="📂 فتح مجلد" Style="{StaticResource ModernButton}" 
                            Click="OpenFolder_Click" Margin="0,0,10,0"/>
                    <Button x:Name="SubtitlesBtn" Content="📝 ترجمة" Style="{StaticResource ModernButton}" 
                            Click="Subtitles_Click" Margin="0,0,10,0"/>
                    <Button x:Name="ScreenshotBtn" Content="📷 لقطة شاشة" Style="{StaticResource ModernButton}" 
                            Click="Screenshot_Click" Margin="0,0,10,0"/>
                    <Button x:Name="SettingsBtn" Content="⚙️ إعدادات" Style="{StaticResource ModernButton}" 
                            Click="Settings_Click" Margin="0,0,10,0"/>
                </StackPanel>
            </Grid>

            <!-- منطقة الفيديو -->
            <Grid Grid.Row="1" Background="Black">
                <!-- مشغل الفيديو -->
                <MediaElement x:Name="mediaPlayer" 
                             LoadedBehavior="Manual" 
                             UnloadedBehavior="Manual"
                             Stretch="Uniform"
                             MediaOpened="MediaPlayer_MediaOpened"
                             MediaEnded="MediaPlayer_MediaEnded"/>

                <!-- شاشة الترحيب -->
                <Grid x:Name="WelcomeScreen">
                    <Grid.Background>
                        <RadialGradientBrush>
                            <GradientStop Color="#FF1A1A2E" Offset="0"/>
                            <GradientStop Color="#FF16213E" Offset="0.7"/>
                            <GradientStop Color="#FF0F3460" Offset="1"/>
                        </RadialGradientBrush>
                    </Grid.Background>

                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                        <!-- أيقونة كبيرة -->
                        <Ellipse Width="120" Height="120" Margin="0,0,0,20">
                            <Ellipse.Fill>
                                <RadialGradientBrush>
                                    <GradientStop Color="#FF0078D4" Offset="0"/>
                                    <GradientStop Color="#FF005A9E" Offset="0.7"/>
                                    <GradientStop Color="#FF004578" Offset="1"/>
                                </RadialGradientBrush>
                            </Ellipse.Fill>
                        </Ellipse>
                        
                        <!-- أيقونة التشغيل -->
                        <TextBlock Text="▶" FontSize="40" Foreground="White" 
                                  HorizontalAlignment="Center" Margin="0,-80,0,60"/>

                        <!-- النص الترحيبي -->
                        <TextBlock Text="فتح ملف" FontSize="18" Foreground="#FF0078D4" 
                                  HorizontalAlignment="Center" FontWeight="Bold"/>
                        <TextBlock Text="اسحب الملفات هنا أو انقر على فتح ملف" 
                                  FontSize="14" Foreground="#FFAAAAAA" 
                                  HorizontalAlignment="Center" Margin="0,10,0,0"/>
                    </StackPanel>
                </Grid>

                <!-- أزرار التحكم العائمة -->
                <Grid x:Name="ControlsOverlay" Background="#80000000" 
                      Visibility="Collapsed" MouseLeave="ControlsOverlay_MouseLeave">
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center" 
                               Orientation="Horizontal">
                        <Button x:Name="PrevBtn" Content="⏮" FontSize="24" Width="60" Height="60" 
                                Style="{StaticResource ModernButton}" Click="Previous_Click" Margin="10"/>
                        <Button x:Name="PlayPauseBtn" Content="▶" FontSize="32" Width="80" Height="80" 
                                Style="{StaticResource ModernButton}" Click="PlayPause_Click" Margin="10"/>
                        <Button x:Name="NextBtn" Content="⏭" FontSize="24" Width="60" Height="60" 
                                Style="{StaticResource ModernButton}" Click="Next_Click" Margin="10"/>
                    </StackPanel>
                </Grid>
            </Grid>

            <!-- شريط التحكم السفلي -->
            <Grid Grid.Row="2" Height="80" Background="#FF1A1A1A">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- شريط التقدم -->
                <Grid Grid.Row="0" Margin="20,10,20,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock x:Name="CurrentTimeText" Grid.Column="0" Text="00:00" 
                              Foreground="White" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    
                    <Slider x:Name="ProgressSlider" Grid.Column="1" 
                           Style="{StaticResource ModernSlider}"
                           ValueChanged="ProgressSlider_ValueChanged"
                           PreviewMouseLeftButtonDown="ProgressSlider_PreviewMouseLeftButtonDown"
                           PreviewMouseLeftButtonUp="ProgressSlider_PreviewMouseLeftButtonUp"/>
                    
                    <TextBlock x:Name="TotalTimeText" Grid.Column="2" Text="00:00" 
                              Foreground="White" VerticalAlignment="Center" Margin="10,0,0,0"/>
                </Grid>

                <!-- أزرار التحكم -->
                <Grid Grid.Row="1" Margin="20,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- أزرار التشغيل -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                        <Button x:Name="PreviousBtn" Content="⏮" FontSize="16" 
                                Style="{StaticResource ModernButton}" Click="Previous_Click" Margin="0,0,5,0"/>
                        <Button x:Name="PlayPauseButton" Content="▶" FontSize="20" 
                                Style="{StaticResource ModernButton}" Click="PlayPause_Click" Margin="0,0,5,0"/>
                        <Button x:Name="StopBtn" Content="⏹" FontSize="16" 
                                Style="{StaticResource ModernButton}" Click="Stop_Click" Margin="0,0,5,0"/>
                        <Button x:Name="NextButton" Content="⏭" FontSize="16" 
                                Style="{StaticResource ModernButton}" Click="Next_Click" Margin="0,0,15,0"/>
                    </StackPanel>

                    <!-- معلومات الملف -->
                    <StackPanel Grid.Column="1" VerticalAlignment="Center" Margin="20,0">
                        <TextBlock x:Name="FileNameText" Text="لا يوجد ملف محدد" 
                                  Foreground="White" FontSize="12" FontWeight="Bold"/>
                        <TextBlock x:Name="FileInfoText" Text="" 
                                  Foreground="#FFAAAAAA" FontSize="10"/>
                    </StackPanel>

                    <!-- تحكم الصوت والإعدادات -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                        <Button x:Name="MuteBtn" Content="🔊" FontSize="16" 
                                Style="{StaticResource ModernButton}" Click="Mute_Click" Margin="0,0,5,0"/>
                        <Slider x:Name="VolumeSlider" Width="80" Minimum="0" Maximum="1" Value="0.5" 
                               Style="{StaticResource ModernSlider}" Margin="0,0,15,0"
                               ValueChanged="VolumeSlider_ValueChanged"/>
                        <Button x:Name="PlaylistBtn" Content="📋" FontSize="16" 
                                Style="{StaticResource ModernButton}" Click="Playlist_Click" Margin="0,0,5,0"/>
                        <Button x:Name="FullscreenBtn" Content="⛶" FontSize="16" 
                                Style="{StaticResource ModernButton}" Click="Fullscreen_Click"/>
                    </StackPanel>
                </Grid>
            </Grid>
        </Grid>
    </Grid>
</Window>
