<script type="text/javascript">
//<![CDATA[
	var browse_target		=	'default';
	$(function(){
		$('#window_browse').dialog({
			autoOpen: false,
			width: 600,
			height: 650,
			modal: true,
			resizable: false,
			buttons: {
				"<?vlc gettext("Open") ?>":function(){
					$('li.ui-selected','#browse_elements').each(function(){
						$(this).dblclick();
					});
				},
				"<?vlc gettext("Enqueue") ?>": function() {
					$('li.ui-selected','#browse_elements').each(function(){
						var path	=	this.getAttribute('opendir') ? this.getAttribute('opendir') : this.getAttribute('openfile');
						switch(browse_target){
							default:
								sendCommand('command=in_enqueue&input='+encodeURI(path));
								setTimeout(function(){updatePlayList(true);},1000);
								break;
						}
					});
					$(this).dialog("close");
				},
				"<?vlc gettext("Cancel") ?>" : function(){
					$(this).dialog("close")
				}
			}
		});
	});
//]]>
</script>

<div id="window_browse" title="<?vlc gettext("Media Browser") ?>">
	<div style="height:500px;overflow: auto;">
		<ol id='browse_elements' selectable="selectable">
			<li><?vlc gettext("Play List") ?></li>
		</ol>
	</div>
</div>
