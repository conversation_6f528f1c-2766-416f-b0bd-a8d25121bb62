<script type="text/javascript">
//<![CDATA[
	$(function(){
		$('#window_mosaic').dialog({
			autoOpen: false,
			width: 800,
			maxWidth: 1000,
			minWidth: 800,
			minHeight: 500,
			modal: true,
			buttons: {
				"<?vlc gettext("Create") ?>": function() {
					$(this).dialog("close");
				},
				"<?vlc gettext("Cancel") ?>" : function(){
					$(this).dialog("close")
				}
			}
		});
		$('#mosaic_bg').resizable({
			maxWidth: 780,
			ghost: true
		});
		$('#mosaic_tiles').draggable({
			maxWidth: 780,
			handle: 'h3',
			containment: [13,98,99999999,99999999],
			drag:function(event,ui){
				var xoff	=	ui.offset.left - $('#mosaic_bg').offset().left;
				var yoff	=	ui.offset.top - $('#mosaic_bg').offset().top-17;
				$('#mosaic_xoff').val(xoff);
				$('#mosaic_yoff').val(yoff);
			}
		});
		$('input','#mosaic_options').change(setMosaic);
		setMosaic();
	});
	function setMosaic(){
		var rows	=	Number($('#mosaic_rows').val());
		var cols	=	Number($('#mosaic_cols').val());
		var n		=	0;
		$('#mosaic_tiles').empty()
		$('#mosaic_tiles').append('<tr><td colspan="99"><h3 style="margin:0px;cursor:move; font-weight:normal" class="ui-widget-header"><?vlc gettext("Mosaic Tiles") ?></h3></td></tr>');
		for(var i=0;i<rows;i++){
			$('#mosaic_tiles').append('<tr>');
			for(var j=0;j<cols;j++){
				$('tr:last','#mosaic_tiles').append('<td class="mosaic">');
				$('td:last','#mosaic_tiles').append('<div id="mosaic_open__'+n+'" class="button icon ui-widget ui-state-default" title="Open Media" style="margin-top:49%"><span class="ui-icon ui-icon-eject"></span></div>');
				n++;
			}
		}
		$('.mosaic').resizable({
			alsoResize: '.mosaic',
			resize:function(event,ui){
				$('#mosaic_width').val(ui.size.width);
				$('#mosaic_height').val(ui.size.height);
				$('[id^=mosaic_open]').css({
					'margin-top': Number($('#mosaic_height').val()/2)
				});
			}
		});
		$('.mosaic').css({
			'background': '#33FF33',
			'width': Number($('#mosaic_width').val()),
			'height':Number($('#mosaic_height').val()),
			'text-align': 'center',
			'float' : 'left',
			'border' : '1px solid #990000',
			'margin-left': Number($('#mosaic_rbord').val()),
			'margin-right': Number($('#mosaic_rbord').val()),
			'margin-top': Number($('#mosaic_cbord').val()),
			'margin-bottom': Number($('#mosaic_cbord').val())
		});
		$('[id^=mosaic_open_]').each(function(){
			$(this).css({
				'margin-top': Number($('#mosaic_height').val()/2)
			});
			$(this).click(function(){
				browse_target	=	'#'+$(this).attr('id');
				get_dir();
				$('#window_browse').dialog('open');
			});
		});

		$('.button').hover(
			function() { $(this).addClass('ui-state-hover'); },
			function() { $(this).removeClass('ui-state-hover'); }
		);
	}
//]]>
</script>

<div id="window_mosaic" title="<?vlc gettext("Create Mosaic") ?>">
	<table id="mosaic_options">
		<tr>
			<td style="text-align:right"><?vlc gettext("Rows") ?></td>
			<td>
				<input type="text" name="mosaic_rows" id="mosaic_rows" size="3" value="2"/>
			</td>
			<td style="text-align:right"><?vlc gettext("X offset") ?></td>
			<td>
				<input type="text" name="mosaic_xoff" id="mosaic_xoff" size="3" value="0" disabled="disabled"/>
			</td>
			<td style="text-align:right"><?vlc gettext("Row border") ?></td>
			<td>
				<input type="text" name="mosaic_rbord" id="mosaic_rbord" size="3" value="5"/>
			</td>
			<td style="text-align:right"><?vlc gettext("Width") ?></td>
			<td>
				<input type="text" name="mosaic_width" id="mosaic_width" size="3" value="100" disabled="disabled"/>
			</td>
		</tr>
		<tr>
			<td style="text-align:right"><?vlc gettext("Columns") ?></td>
			<td>
				<input type="text" name="mosaic_cols" id="mosaic_cols" size="3" value="2"/>
			</td>
			<td style="text-align:right"><?vlc gettext("Y offset") ?></td>
			<td>
				<input type="text" name="mosaic_yoff" id="mosaic_yoff" size="3" value="0" disabled="disabled"/>
			</td>
			<td style="text-align:right"><?vlc gettext("Column border") ?></td>
			<td>
				<input type="text" name="mosaic_cbord" id="mosaic_cbord" size="3" value="5"/>
			</td>
			<td style="text-align:right"><?vlc gettext("Height") ?></td>
			<td>
				<input type="text" name="mosaic_height" id="mosaic_height" size="3" value="100" disabled="disabled"/>
			</td>
		</tr>
	</table>
	<div id="mosaic_bg" class="ui-widget-content" style="background: #3333FF;width:400px; height:300px;text-align: center; vertical-align: middle;">
		<h3 style="margin:0px;font-weight:normal" class="ui-widget-header"><?vlc gettext("Background") ?></h3>
		<table id="mosaic_tiles" class="ui-widget-content" cellpadding="0" cellspacing="0">
			<tr><td colspan="99"><h3 style="margin:0px;cursor:move; font-weight:normal" class="ui-widget-header"><?vlc gettext("Mosaic Tiles") ?></h3></td></tr>
			<tr>
				<td class="mosaic"></td>
				<td class="mosaic"></td>
			</tr>
			<tr>
				<td class="mosaic"></td>
				<td class="mosaic"></td>
			</tr>
		</table>
	</div>
</div>
