using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Threading;
using Microsoft.Win32;

namespace MediaPlayerApp
{
    public partial class QQPlayerWindow : Window
    {
        private DispatcherTimer? timer;
        private bool isDragging = false;
        private bool isPlaying = false;
        private bool isMuted = false;
        private double previousVolume = 0.5;
        private List<string> playlist = new List<string>();
        private int currentTrackIndex = 0;

        public QQPlayerWindow()
        {
            InitializeComponent();
            InitializeTimer();
            InitializeEventHandlers();
            this.Loaded += QQPlayerWindow_Loaded;
        }

        private void InitializeEventHandlers()
        {
            // دعم السحب والإفلات
            this.AllowDrop = true;
            this.Drop += QQPlayerWindow_Drop;
            this.DragEnter += QQPlayerWindow_DragEnter;

            // اختصارات لوحة المفاتيح
            this.KeyDown += QQPlayerWindow_KeyDown;

            // إظهار/إخفاء أزرار التحكم
            mediaPlayer.MouseEnter += MediaPlayer_MouseEnter;
            mediaPlayer.MouseMove += MediaPlayer_MouseMove;
        }

        private void QQPlayerWindow_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                if (mediaPlayer != null)
                    mediaPlayer.Volume = 0.5;

                if (VolumeSlider != null)
                    VolumeSlider.Value = 0.5;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة التطبيق: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void InitializeTimer()
        {
            timer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            timer.Tick += Timer_Tick;
        }

        private void Timer_Tick(object? sender, EventArgs e)
        {
            try
            {
                if (mediaPlayer?.Source != null && mediaPlayer.NaturalDuration.HasTimeSpan && !isDragging)
                {
                    var currentTime = mediaPlayer.Position;
                    var totalTime = mediaPlayer.NaturalDuration.TimeSpan;

                    if (CurrentTimeText != null)
                        CurrentTimeText.Text = FormatTime(currentTime);

                    if (TotalTimeText != null)
                        TotalTimeText.Text = FormatTime(totalTime);

                    if (totalTime.TotalSeconds > 0 && ProgressSlider != null)
                    {
                        ProgressSlider.Value = (currentTime.TotalSeconds / totalTime.TotalSeconds) * 100;
                    }
                }
            }
            catch
            {
                // تجاهل أخطاء Timer
            }
        }

        private string FormatTime(TimeSpan time)
        {
            return $"{(int)time.TotalMinutes:D2}:{time.Seconds:D2}";
        }

        // أحداث شريط العنوان
        private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ClickCount == 2)
            {
                MaximizeButton_Click(sender, e);
            }
            else
            {
                this.DragMove();
            }
        }

        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            this.WindowState = WindowState.Minimized;
        }

        private void MaximizeButton_Click(object sender, RoutedEventArgs e)
        {
            if (this.WindowState == WindowState.Maximized)
            {
                this.WindowState = WindowState.Normal;
                MaximizeButton.Content = "🗖";
            }
            else
            {
                this.WindowState = WindowState.Maximized;
                MaximizeButton.Content = "🗗";
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        // السحب والإفلات
        private void QQPlayerWindow_DragEnter(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent(DataFormats.FileDrop))
                e.Effects = DragDropEffects.Copy;
            else
                e.Effects = DragDropEffects.None;
        }

        private void QQPlayerWindow_Drop(object sender, DragEventArgs e)
        {
            try
            {
                string[] files = (string[])e.Data.GetData(DataFormats.FileDrop);
                if (files != null && files.Length > 0)
                {
                    LoadFiles(files);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الملفات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // اختصارات لوحة المفاتيح
        private void QQPlayerWindow_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.Key)
            {
                case Key.Space:
                    PlayPause_Click(sender, e);
                    break;
                case Key.S:
                    Stop_Click(sender, e);
                    break;
                case Key.O:
                    if (Keyboard.Modifiers == ModifierKeys.Control)
                        OpenFile_Click(sender, e);
                    break;
                case Key.Right:
                    Next_Click(sender, e);
                    break;
                case Key.Left:
                    Previous_Click(sender, e);
                    break;
                case Key.Up:
                    if (VolumeSlider != null && VolumeSlider.Value < 1.0)
                        VolumeSlider.Value = Math.Min(1.0, VolumeSlider.Value + 0.1);
                    break;
                case Key.Down:
                    if (VolumeSlider != null && VolumeSlider.Value > 0.0)
                        VolumeSlider.Value = Math.Max(0.0, VolumeSlider.Value - 0.1);
                    break;
                case Key.F11:
                    Fullscreen_Click(sender, e);
                    break;
                case Key.M:
                    Mute_Click(sender, e);
                    break;
            }
        }

        // إظهار أزرار التحكم
        private void MediaPlayer_MouseEnter(object sender, MouseEventArgs e)
        {
            if (ControlsOverlay != null)
                ControlsOverlay.Visibility = Visibility.Visible;
        }

        private void MediaPlayer_MouseMove(object sender, MouseEventArgs e)
        {
            if (ControlsOverlay != null)
                ControlsOverlay.Visibility = Visibility.Visible;
        }

        private void ControlsOverlay_MouseLeave(object sender, MouseEventArgs e)
        {
            if (ControlsOverlay != null)
                ControlsOverlay.Visibility = Visibility.Collapsed;
        }

        private void LoadFiles(string[] files)
        {
            var mediaFiles = files.Where(f => IsMediaFile(f)).ToList();
            if (mediaFiles.Any())
            {
                playlist.Clear();
                playlist.AddRange(mediaFiles);
                currentTrackIndex = 0;
                LoadCurrentTrack();
            }
        }

        private bool IsMediaFile(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLower();
            return new[] { ".mp4", ".avi", ".wmv", ".mov", ".mkv", ".mp3", ".wav", ".wma", ".flac", ".m4a",
                          ".m4v", ".3gp", ".webm", ".ogg", ".ts", ".mts", ".vob" }
                   .Contains(extension);
        }

        private void LoadCurrentTrack()
        {
            if (playlist.Count > 0 && currentTrackIndex >= 0 && currentTrackIndex < playlist.Count)
            {
                try
                {
                    var filePath = playlist[currentTrackIndex];
                    if (mediaPlayer != null)
                    {
                        mediaPlayer.Source = new Uri(filePath);
                        mediaPlayer.LoadedBehavior = MediaState.Manual;
                    }

                    var fileName = Path.GetFileName(filePath);
                    Title = $"QQPlayer - {fileName}";

                    if (FileNameText != null)
                        FileNameText.Text = fileName;

                    if (FileInfoText != null)
                    {
                        var fileInfo = new FileInfo(filePath);
                        FileInfoText.Text = $"الحجم: {FormatFileSize(fileInfo.Length)} | النوع: {Path.GetExtension(filePath).ToUpper()}";
                    }

                    if (WelcomeScreen != null)
                        WelcomeScreen.Visibility = Visibility.Collapsed;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تحميل الملف: {ex.Message}", "خطأ",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        // أحداث أزرار القائمة
        private void OpenFile_Click(object sender, RoutedEventArgs e)
        {
            OpenFileDialog openFileDialog = new OpenFileDialog
            {
                Multiselect = true,
                Filter = "جميع ملفات الوسائط|*.mp4;*.avi;*.wmv;*.mov;*.mkv;*.mp3;*.wav;*.wma;*.flac;*.m4a;*.m4v;*.3gp;*.webm;*.ogg;*.ts;*.mts;*.vob|" +
                        "ملفات الفيديو|*.mp4;*.avi;*.wmv;*.mov;*.mkv;*.m4v;*.3gp;*.webm;*.ts;*.mts;*.vob|" +
                        "ملفات الصوت|*.mp3;*.wav;*.wma;*.flac;*.m4a;*.ogg|" +
                        "جميع الملفات|*.*"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                try
                {
                    LoadFiles(openFileDialog.FileNames);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في فتح الملف: {ex.Message}", "خطأ",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void OpenFolder_Click(object sender, RoutedEventArgs e)
        {
            // سيتم تنفيذه لاحق<|im_start|>
            MessageBox.Show("ميزة فتح المجلد ستكون متاحة قريب<|im_start|>", "قريب<|im_start|>",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void Subtitles_Click(object sender, RoutedEventArgs e)
        {
            // سيتم تنفيذه لاحق<|im_start|>
            MessageBox.Show("ميزة الترجمة ستكون متاحة قريب<|im_start|>", "قريب<|im_start|>",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void Screenshot_Click(object sender, RoutedEventArgs e)
        {
            // سيتم تنفيذه لاحق<|im_start|>
            MessageBox.Show("ميزة لقطة الشاشة ستكون متاحة قريب<|im_start|>", "قريب<|im_start|>",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void Settings_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var settingsWindow = new SettingsWindow
                {
                    Owner = this
                };
                settingsWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح الإعدادات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // أحداث التحكم في التشغيل
        private void PlayPause_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (mediaPlayer?.Source == null)
                {
                    if (playlist.Count == 0)
                    {
                        MessageBox.Show("يرجى فتح ملف وسائط أولاً", "تنبيه",
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                        return;
                    }
                    LoadCurrentTrack();
                }

                if (isPlaying)
                {
                    mediaPlayer?.Pause();
                    if (PlayPauseButton != null)
                        PlayPauseButton.Content = "▶";
                    if (PlayPauseBtn != null)
                        PlayPauseBtn.Content = "▶";
                    timer?.Stop();
                    isPlaying = false;
                }
                else
                {
                    mediaPlayer?.Play();
                    if (PlayPauseButton != null)
                        PlayPauseButton.Content = "⏸";
                    if (PlayPauseBtn != null)
                        PlayPauseBtn.Content = "⏸";
                    timer?.Start();
                    isPlaying = true;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التشغيل: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Stop_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                mediaPlayer?.Stop();
                if (PlayPauseButton != null)
                    PlayPauseButton.Content = "▶";
                if (PlayPauseBtn != null)
                    PlayPauseBtn.Content = "▶";
                timer?.Stop();
                isPlaying = false;
                if (ProgressSlider != null)
                    ProgressSlider.Value = 0;
                if (CurrentTimeText != null)
                    CurrentTimeText.Text = "00:00";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الإيقاف: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Previous_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (playlist.Count > 0)
                {
                    currentTrackIndex = currentTrackIndex > 0 ? currentTrackIndex - 1 : playlist.Count - 1;
                    LoadCurrentTrack();
                    if (isPlaying && mediaPlayer != null)
                    {
                        mediaPlayer.Play();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التنقل: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Next_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (playlist.Count > 0)
                {
                    currentTrackIndex = currentTrackIndex < playlist.Count - 1 ? currentTrackIndex + 1 : 0;
                    LoadCurrentTrack();
                    if (isPlaying && mediaPlayer != null)
                    {
                        mediaPlayer.Play();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التنقل: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // أحداث شريط التقدم
        private void ProgressSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            try
            {
                if (isDragging && mediaPlayer?.NaturalDuration.HasTimeSpan == true && ProgressSlider != null)
                {
                    var totalSeconds = mediaPlayer.NaturalDuration.TimeSpan.TotalSeconds;
                    var newPosition = TimeSpan.FromSeconds((ProgressSlider.Value / 100) * totalSeconds);
                    mediaPlayer.Position = newPosition;
                }
            }
            catch
            {
                // تجاهل أخطاء شريط التقدم
            }
        }

        private void ProgressSlider_PreviewMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            isDragging = true;
        }

        private void ProgressSlider_PreviewMouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            try
            {
                isDragging = false;
                if (mediaPlayer?.NaturalDuration.HasTimeSpan == true && ProgressSlider != null)
                {
                    var totalSeconds = mediaPlayer.NaturalDuration.TimeSpan.TotalSeconds;
                    var newPosition = TimeSpan.FromSeconds((ProgressSlider.Value / 100) * totalSeconds);
                    mediaPlayer.Position = newPosition;
                }
            }
            catch
            {
                // تجاهل أخطاء شريط التقدم
            }
        }

        // تحكم الصوت
        private void VolumeSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            try
            {
                if (mediaPlayer != null && VolumeSlider != null)
                {
                    mediaPlayer.Volume = VolumeSlider.Value;
                    UpdateMuteButton();
                }
            }
            catch
            {
                // تجاهل أخطاء تغيير الصوت
            }
        }

        private void Mute_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (isMuted)
                {
                    // إلغاء الكتم
                    if (VolumeSlider != null)
                        VolumeSlider.Value = previousVolume;
                    isMuted = false;
                }
                else
                {
                    // كتم الصوت
                    if (VolumeSlider != null)
                    {
                        previousVolume = VolumeSlider.Value;
                        VolumeSlider.Value = 0;
                    }
                    isMuted = true;
                }
                UpdateMuteButton();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في كتم الصوت: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateMuteButton()
        {
            if (MuteBtn != null)
            {
                if (VolumeSlider?.Value == 0 || isMuted)
                    MuteBtn.Content = "🔇";
                else if (VolumeSlider?.Value < 0.5)
                    MuteBtn.Content = "🔉";
                else
                    MuteBtn.Content = "🔊";
            }
        }

        // أحداث أخرى
        private void Playlist_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var playlistWindow = new PlaylistWindow
                {
                    Owner = this
                };

                // تحميل قائمة التشغيل الحالية
                playlistWindow.LoadPlaylist(playlist);

                // ربط حدث اختيار العنصر
                playlistWindow.ItemSelected += (s, item) =>
                {
                    var index = playlist.IndexOf(item.FilePath);
                    if (index >= 0)
                    {
                        currentTrackIndex = index;
                        LoadCurrentTrack();
                        if (isPlaying)
                        {
                            mediaPlayer?.Play();
                        }
                    }
                };

                playlistWindow.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح قائمة التشغيل: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Fullscreen_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (this.WindowState == WindowState.Maximized && this.WindowStyle == WindowStyle.None)
                {
                    // الخروج من وضع ملء الشاشة
                    this.WindowState = WindowState.Normal;
                    this.WindowStyle = WindowStyle.None;
                    if (FullscreenBtn != null)
                        FullscreenBtn.Content = "⛶";
                }
                else
                {
                    // دخول وضع ملء الشاشة
                    this.WindowState = WindowState.Maximized;
                    this.WindowStyle = WindowStyle.None;
                    if (FullscreenBtn != null)
                        FullscreenBtn.Content = "🗗";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في وضع ملء الشاشة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // أحداث مشغل الوسائط
        private void MediaPlayer_MediaOpened(object sender, RoutedEventArgs e)
        {
            try
            {
                if (mediaPlayer?.NaturalDuration.HasTimeSpan == true && TotalTimeText != null)
                {
                    TotalTimeText.Text = FormatTime(mediaPlayer.NaturalDuration.TimeSpan);
                }
            }
            catch
            {
                // تجاهل أخطاء فتح الوسائط
            }
        }

        private void MediaPlayer_MediaEnded(object sender, RoutedEventArgs e)
        {
            // تشغيل الملف التالي تلقائي<|im_start|>
            Next_Click(sender, e);
        }

        protected override void OnClosed(EventArgs e)
        {
            timer?.Stop();
            mediaPlayer?.Close();
            base.OnClosed(e);
        }
    }
}
