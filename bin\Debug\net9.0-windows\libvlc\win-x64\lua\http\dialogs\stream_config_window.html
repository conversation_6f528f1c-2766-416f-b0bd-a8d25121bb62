<script type="text/javascript">
	$(function(){
		$('#window_stream_config').dialog({
			autoOpen: false,
			width:400,
			modal: true,
			buttons:{
				"<?vlc gettext("Okay") ?>":function(){
					$('#player').empty();
					$('#player').attr('href',$('#stream_protocol').val()+'://'+$('#stream_host').val()+':'+$('#stream_port').val()+'/'+$('#stream_file').val());
					flowplayer("player", "https://releases.flowplayer.org/swf/flowplayer-3.2.7.swf");
					$(this).dialog('close');
				},
				"<?vlc gettext("Cancel") ?>":function(){
					$(this).dialog('close');
				}
			}
		});
	})
</script>
<div id="window_stream_config" title="<?vlc gettext("Stream Input Configuration") ?>">
	<table>
		<tr>
			<td><?vlc gettext("Protocol") ?></td>
			<td><input type="text" name="stream_protocol" id="stream_protocol" value="http" /></td>
		</tr>
		<tr>
			<td><?vlc gettext("Host") ?></td>
			<td><input type="text" name="stream_host" id="stream_host" value="" /></td>
		</tr>
		<tr>
			<td><?vlc gettext("Port") ?></td>
			<td><input type="text" name="stream_port" id="stream_port" value="8081" /></td>
		</tr>
		<tr>
			<td><?vlc gettext("File") ?></td>
			<td><input type="text" name="stream_file" id="stream_file" value="stream.flv" /></td>
		</tr>
	</table>
</div>
