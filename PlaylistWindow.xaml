<Window x:Class="MediaPlayerApp.PlaylistWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="📋 قائمة التشغيل - QQPlayer" 
        Height="500" Width="400"
        WindowStartupLocation="CenterOwner"
        Background="#FF1A1A1A"
        WindowStyle="None"
        AllowsTransparency="True"
        ResizeMode="CanResize">

    <Window.Resources>
        <SolidColorBrush x:Key="PrimaryBlue" Color="#FF0078D4"/>
        <SolidColorBrush x:Key="DarkBackground" Color="#FF1A1A1A"/>
        <SolidColorBrush x:Key="ControlBackground" Color="#FF2D2D2D"/>
        <SolidColorBrush x:Key="HoverBlue" Color="#FF106EBE"/>
        
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource ControlBackground}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="3" Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource HoverBlue}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="PlaylistItem" TargetType="ListBoxItem">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ListBoxItem">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="3" Padding="{TemplateBinding Padding}">
                            <ContentPresenter/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#FF333333"/>
                            </Trigger>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter Property="Background" Value="{StaticResource PrimaryBlue}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Border BorderBrush="{StaticResource PrimaryBlue}" BorderThickness="2" CornerRadius="8">
        <Grid>
            <Grid.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                    <GradientStop Color="#FF1A1A1A" Offset="0"/>
                    <GradientStop Color="#FF2D2D2D" Offset="1"/>
                </LinearGradientBrush>
            </Grid.Background>

            <Grid.RowDefinitions>
                <RowDefinition Height="40"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="50"/>
            </Grid.RowDefinitions>

            <!-- شريط العنوان -->
            <Grid Grid.Row="0" Background="{StaticResource PrimaryBlue}" 
                  MouseLeftButtonDown="TitleBar_MouseLeftButtonDown">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" Margin="10,0" VerticalAlignment="Center">
                    <TextBlock Text="📋" FontSize="16" Margin="0,0,8,0"/>
                    <TextBlock Text="قائمة التشغيل" FontSize="14" FontWeight="Bold" Foreground="White"/>
                    <TextBlock x:Name="PlaylistCount" Text="(0 ملف)" FontSize="12" 
                              Foreground="#FFCCCCCC" Margin="10,0,0,0"/>
                </StackPanel>

                <Button Grid.Column="1" x:Name="CloseBtn" Content="✕" Width="40" Height="40" 
                        Background="Transparent" Foreground="White" BorderThickness="0"
                        Click="CloseBtn_Click" FontSize="14"/>
            </Grid>

            <!-- قائمة الملفات -->
            <Grid Grid.Row="1" Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- شريط البحث -->
                <Border Grid.Row="0" Background="{StaticResource ControlBackground}" 
                        CornerRadius="5" Margin="0,0,0,10">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="🔍" FontSize="14" Foreground="#FFAAAAAA" 
                                  VerticalAlignment="Center" Margin="10,0,5,0"/>
                        
                        <TextBox x:Name="SearchBox" Grid.Column="1" Background="Transparent" 
                                Foreground="White" BorderThickness="0" Padding="5"
                                Text="البحث في قائمة التشغيل..." FontSize="12"
                                GotFocus="SearchBox_GotFocus" LostFocus="SearchBox_LostFocus"
                                TextChanged="SearchBox_TextChanged"/>
                        
                        <Button Grid.Column="2" x:Name="ClearSearchBtn" Content="✕" Width="25" Height="25" 
                                Background="Transparent" Foreground="#FFAAAAAA" BorderThickness="0"
                                Click="ClearSearchBtn_Click" FontSize="10" Margin="0,0,5,0"/>
                    </Grid>
                </Border>

                <!-- قائمة الملفات -->
                <ListBox x:Name="PlaylistBox" Grid.Row="1" Background="Transparent" 
                        BorderThickness="0" ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                        ItemContainerStyle="{StaticResource PlaylistItem}"
                        SelectionChanged="PlaylistBox_SelectionChanged"
                        MouseDoubleClick="PlaylistBox_MouseDoubleClick">
                    <ListBox.ItemTemplate>
                        <DataTemplate>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Column="0" Text="🎵" FontSize="12" 
                                          VerticalAlignment="Center" Margin="0,0,8,0"/>
                                
                                <StackPanel Grid.Column="1">
                                    <TextBlock Text="{Binding FileName}" FontWeight="Bold" 
                                              FontSize="12" TextTrimming="CharacterEllipsis"/>
                                    <TextBlock Text="{Binding FileInfo}" FontSize="10" 
                                              Foreground="#FFAAAAAA" TextTrimming="CharacterEllipsis"/>
                                </StackPanel>

                                <TextBlock Grid.Column="2" Text="{Binding Duration}" 
                                          FontSize="10" Foreground="#FFAAAAAA" 
                                          VerticalAlignment="Center" Margin="5,0,0,0"/>
                            </Grid>
                        </DataTemplate>
                    </ListBox.ItemTemplate>
                </ListBox>

                <!-- رسالة القائمة الفارغة -->
                <StackPanel x:Name="EmptyPlaylist" Grid.Row="1" HorizontalAlignment="Center" 
                           VerticalAlignment="Center" Visibility="Visible">
                    <TextBlock Text="📭" FontSize="48" HorizontalAlignment="Center" 
                              Foreground="#FF666666" Margin="0,0,0,10"/>
                    <TextBlock Text="قائمة التشغيل فارغة" FontSize="16" 
                              Foreground="#FF666666" HorizontalAlignment="Center"/>
                    <TextBlock Text="اسحب الملفات هنا أو استخدم زر فتح ملف" 
                              FontSize="12" Foreground="#FF888888" 
                              HorizontalAlignment="Center" Margin="0,5,0,0"/>
                </StackPanel>
            </Grid>

            <!-- أزرار التحكم -->
            <Grid Grid.Row="2" Background="#FF2D2D2D">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- أزرار التحكم اليسار -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" Margin="10,10" VerticalAlignment="Center">
                    <Button x:Name="AddFilesBtn" Content="📁 إضافة" Style="{StaticResource ModernButton}" 
                            Click="AddFilesBtn_Click" Margin="0,0,5,0"/>
                    <Button x:Name="RemoveBtn" Content="🗑️ حذف" Style="{StaticResource ModernButton}" 
                            Click="RemoveBtn_Click" Margin="0,0,5,0"/>
                    <Button x:Name="ClearAllBtn" Content="🧹 مسح الكل" Style="{StaticResource ModernButton}" 
                            Click="ClearAllBtn_Click"/>
                </StackPanel>

                <!-- أزرار التحكم اليمين -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="10,10" VerticalAlignment="Center">
                    <Button x:Name="ShuffleBtn" Content="🔀 خلط" Style="{StaticResource ModernButton}" 
                            Click="ShuffleBtn_Click" Margin="0,0,5,0"/>
                    <Button x:Name="SavePlaylistBtn" Content="💾 حفظ" Style="{StaticResource ModernButton}" 
                            Click="SavePlaylistBtn_Click" Margin="0,0,5,0"/>
                    <Button x:Name="LoadPlaylistBtn" Content="📂 تحميل" Style="{StaticResource ModernButton}" 
                            Click="LoadPlaylistBtn_Click"/>
                </StackPanel>
            </Grid>
        </Grid>
    </Border>
</Window>
