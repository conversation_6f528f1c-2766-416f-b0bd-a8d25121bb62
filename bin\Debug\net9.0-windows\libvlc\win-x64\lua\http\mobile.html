<!--  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - >
<  mobile.html: VLC media player web interface - VLM
< - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - >
<  Copyright (C) 2005-2014 VLC authors and VideoLAN
<
<  Authors: <AUTHORS>
<
<  This program is free software; you can redistribute it and/or modify
<  it under the terms of the GNU General Public License as published by
<  the Free Software Foundation; either version 2 of the License, or
<  (at your option) any later version.
<
<  This program is distributed in the hope that it will be useful,
<  but WITHOUT ANY WARRANTY; without even the implied warranty of
<  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
<  GNU General Public License for more details.
<
<  You should have received a copy of the GNU General Public License
<  along with this program; if not, write to the Free Software
<  Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston MA 02110-1301, USA.
< - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -->
<html>
	<head>
		<title>VLC media player - Web Interface</title>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<meta name="referrer" content="no-referrer" />
		<meta name="viewport" content="initial-scale=1.0" />
		<meta name="viewport" content="width=device-width" />
		<meta name="apple-mobile-web-app-status-bar-style" content="default" />
		<link href="favicon.ico" type="image/x-icon" rel="shortcut icon" />
		<script type="text/javascript" src="js/common.js"></script>
		<link type="text/css" href="css/ui-lightness/jquery-ui-1.8.13.custom.css" rel="stylesheet" />
		<link type="text/css" href="css/mobile.css" rel="stylesheet" />
		<script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.6.1/jquery.min.js"></script>
		<script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.8.13/jquery-ui.min.js"></script>
		<script type="text/javascript" src="js/jquery.jstree.js"></script>
		<script type="text/javascript" src="https://releases.flowplayer.org/js/flowplayer-3.2.6.min.js"></script>
		<script type="text/javascript" src="js/ui.js"></script>
		<script type="text/javascript" src="js/controllers.js"></script>
		<script type="text/javascript">
			var pollStatus	=	true;
			$(function(){
				$('#buttonPrev').click(function(){
					sendCommand({'command':'pl_previous'});
					return false;
				});
				$('#buttonNext').click(function(){
					sendCommand({'command':'pl_next'});
					return false;
				});
				$('#buttonOpen').click(function(){
					window.location='mobile_browse.html';
				});
				$('#buttonEQ').click(function(){
					window.location='mobile_equalizer.html';
				});
				$('#buttonSout').click(function(){
					var file			=	  decodeURIComponent($('#buttonPlay').attr('mrl'));
					if(file){
						var defaultStream	=	'new Current broadcast enabled input "'+file+'" output #transcode{vcodec=FLV1,vb=4096,fps=25,scale=1,acodec=mp3,ab=512,samplerate=44100,channels=2}:std{access='+$('#stream_protocol').val()+',mux=avformat{{mux=flv}},dst=0.0.0.0:'+$('#stream_port').val()+'/'+$('#stream_file').val()+'}';
						sendVLMCmd('del Current;'+defaultStream+';control Current play','window.location="mobile_view.html"');
					}
					return false;
				});
                $('#buttonRewd').click(function(){
                    sendCommand({'command': 'seek', 'val': '-30S'});
                    return false;
                });
                $('#buttonFwrd').click(function(){
                    sendCommand({'command': 'seek', 'val': '+30S'});
                    return false;
                });
			})
		</script>
	</head>
	<body>
	<div id="content" class="centered">
		<div id="art">
			<img id="albumArt" src="/art" />
		</div>
		<div style="width:100%"><div id="mediaTitle" class="dynamic centered"></div></div>
		<div id="play_controls">
			<div id="buttons">
				<div id="buttonPrev" class="button48  ui-corner-all" title="Previous">
				</div><div id="buttonRewd" class="button48  ui-corner-all" title="Rewind">
				</div><div id="buttonPlay" class="button48  ui-corner-all paused" title="Play">
				</div><div id="buttonStop" class="button48  ui-corner-all" title="Stop">
				</div><div id="buttonFwrd" class="button48  ui-corner-all" title="Forward">
				</div><div id="buttonNext" class="button48  ui-corner-all" title="Next"></div>
			</div>
			<div id="seekSlider" title="Seek Time" style="font-size:15px;"></div>
			<div id="currentTime" class="dynamic">00:00:00</div>
			<div id="totalTime" class="dynamic">00:00:00</div>
		</div>
		<div id="controls">
			<div id="buttons">
				<div id="buttonOpen" class="button48  ui-corner-all" title="Open Media">
				</div><div id="buttonFull" class="button48  ui-corner-all" title="Full Screen">
				</div><div id="buttonEQ" class="button48 ui-corner-all" title="Equalizer">
				</div><div id="buttonSout" class="button48  ui-corner-all" title="Easy Stream"></div>
			</div>
			<div id="volumeSlider" title="Volume" style="font-size:15px;">
				<img src="images/speaker-32.png" class="ui-slider-handle" alt="volume"/>
			</div>
		</div>
		<?vlc

	dialogs("stream_config_window.html");
	?>
	</div>
	</body>
</html>
