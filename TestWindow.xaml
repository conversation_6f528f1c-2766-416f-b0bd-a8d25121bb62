<Window x:Class="MediaPlayerApp.TestWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="اختبار النافذة - Test Window" Height="300" Width="400"
        WindowStartupLocation="CenterScreen">
    <Grid Background="LightBlue">
        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
            <TextBlock Text="مرحباً! النافذة تعمل بشكل صحيح" 
                      FontSize="16" 
                      HorizontalAlignment="Center" 
                      Margin="10"/>
            <TextBlock Text="Hello! The window is working correctly" 
                      FontSize="14" 
                      HorizontalAlignment="Center" 
                      Margin="10"/>
            <Button Content="إغلاق - Close" 
                   Width="100" 
                   Height="30" 
                   Margin="10"
                   Click="CloseButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
