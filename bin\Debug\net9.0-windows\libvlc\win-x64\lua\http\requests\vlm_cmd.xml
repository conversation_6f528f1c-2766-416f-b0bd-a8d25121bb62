<?xml version="1.0" encoding="utf-8" standalone="yes" ?>
<?vlc --[[
<!--  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - >
<  vlm_cmd.xml: VLC media player web interface
< - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - >
<  Copyright (C) 2005-2006 the VideoLAN team
< 
<  Authors: <AUTHORS>
< 
<  This program is free software; you can redistribute it and/or modify
<  it under the terms of the GNU General Public License as published by
<  the Free Software Foundation; either version 2 of the License, or
<  (at your option) any later version.
< 
<  This program is distributed in the hope that it will be useful,
<  but WITHOUT ANY WARRANTY; without even the implied warranty of
<  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
<  GNU General Public License for more details.
< 
<  You should have received a copy of the GNU General Public License
<  along with this program; if not, write to the Free Software
<  Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston MA 02110-1301, USA.
< - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -->
]]?>
<vlm><error><?vlc
if _GET["command"] then
  local msg = vlm:execute_command(_GET["command"])
  if msg.value then
    print(msg.name,":",vlc.strings.convert_xml_special_chars(msg.value))
  end
else
?>No command<?vlc
end
?></error>
</vlm>
