using System;
using System.IO;
using System.Windows;
using System.Windows.Threading;
using Microsoft.Win32;

namespace MediaPlayerApp
{
    public partial class SimpleMainWindow : Window
    {
        private DispatcherTimer? timer;
        private bool isPlaying = false;

        public SimpleMainWindow()
        {
            InitializeComponent();
            InitializeTimer();
            this.Loaded += SimpleMainWindow_Loaded;
        }

        private void SimpleMainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // تهيئة آمنة
                if (mediaPlayer != null)
                    mediaPlayer.Volume = 0.5;
                    
                if (volumeSlider != null)
                {
                    volumeSlider.Value = 0.5;
                    volumeSlider.ValueChanged += VolumeSlider_ValueChanged;
                }

                if (progressSlider != null)
                    progressSlider.ValueChanged += ProgressSlider_ValueChanged;

                MessageBox.Show("مشغل الوسائط البسيط جاهز!", "مرحباً", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void InitializeTimer()
        {
            timer = new DispatcherTimer();
            timer.Interval = TimeSpan.FromSeconds(1);
            timer.Tick += Timer_Tick;
        }

        private void Timer_Tick(object? sender, EventArgs e)
        {
            try
            {
                if (mediaPlayer?.Source != null && mediaPlayer.NaturalDuration.HasTimeSpan)
                {
                    var current = mediaPlayer.Position;
                    var total = mediaPlayer.NaturalDuration.TimeSpan;

                    if (currentTime != null)
                        currentTime.Text = $"{(int)current.TotalMinutes:D2}:{current.Seconds:D2}";
                        
                    if (totalTime != null)
                        totalTime.Text = $"{(int)total.TotalMinutes:D2}:{total.Seconds:D2}";

                    if (progressSlider != null && total.TotalSeconds > 0)
                        progressSlider.Value = (current.TotalSeconds / total.TotalSeconds) * 100;
                }
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }

        private void OpenFile_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var dialog = new OpenFileDialog();
                dialog.Filter = "Media Files|*.mp4;*.avi;*.mp3;*.wav|All Files|*.*";
                
                if (dialog.ShowDialog() == true)
                {
                    if (mediaPlayer != null)
                    {
                        mediaPlayer.Source = new Uri(dialog.FileName);
                        Title = $"🎬 مشغل الوسائط - {Path.GetFileName(dialog.FileName)}";
                        
                        if (welcomeText != null)
                            welcomeText.Visibility = Visibility.Collapsed;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح الملف: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PlayPause_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (mediaPlayer?.Source == null)
                {
                    MessageBox.Show("يرجى فتح ملف أولاً", "تنبيه", 
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                if (isPlaying)
                {
                    mediaPlayer?.Pause();
                    if (playButton != null)
                        playButton.Content = "▶️";
                    timer?.Stop();
                    isPlaying = false;
                }
                else
                {
                    mediaPlayer?.Play();
                    if (playButton != null)
                        playButton.Content = "⏸️";
                    timer?.Start();
                    isPlaying = true;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التشغيل: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Stop_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                mediaPlayer?.Stop();
                if (playButton != null)
                    playButton.Content = "▶️";
                timer?.Stop();
                isPlaying = false;
                if (progressSlider != null)
                    progressSlider.Value = 0;
                if (currentTime != null)
                    currentTime.Text = "00:00";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الإيقاف: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void VolumeSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            try
            {
                if (mediaPlayer != null && volumeSlider != null)
                {
                    mediaPlayer.Volume = volumeSlider.Value;
                    if (volumeText != null)
                        volumeText.Text = $"{(int)(volumeSlider.Value * 100)}%";
                }
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }

        private void ProgressSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            // لا نفعل شيء هنا لتجنب المشاكل
        }

        protected override void OnClosed(EventArgs e)
        {
            timer?.Stop();
            mediaPlayer?.Close();
            base.OnClosed(e);
        }
    }
}
