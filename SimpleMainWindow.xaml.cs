using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Input;
using System.Windows.Threading;
using Microsoft.Win32;

namespace MediaPlayerApp
{
    public partial class SimpleMainWindow : Window
    {
        private DispatcherTimer? timer;
        private bool isPlaying = false;
        private bool isFullscreen = false;
        private bool isShuffle = false;
        private bool isRepeat = false;
        private List<string> playlist = new List<string>();
        private int currentTrackIndex = 0;
        private WindowState previousWindowState;
        private WindowStyle previousWindowStyle;

        public SimpleMainWindow()
        {
            InitializeComponent();
            InitializeTimer();
            InitializeEventHandlers();
            this.Loaded += SimpleMainWindow_Loaded;
        }

        private void InitializeEventHandlers()
        {
            // دعم السحب والإفلات
            this.AllowDrop = true;
            this.Drop += SimpleMainWindow_Drop;
            this.DragEnter += SimpleMainWindow_DragEnter;

            // اختصارات لوحة المفاتيح
            this.KeyDown += SimpleMainWindow_KeyDown;
        }

        private void SimpleMainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // تهيئة آمنة
                if (mediaPlayer != null)
                {
                    mediaPlayer.Volume = 0.5;
                    mediaPlayer.MediaOpened += MediaPlayer_MediaOpened;
                    mediaPlayer.MediaEnded += MediaPlayer_MediaEnded;
                }

                if (volumeSlider != null)
                {
                    volumeSlider.Value = 0.5;
                    volumeSlider.ValueChanged += VolumeSlider_ValueChanged;
                }

                if (progressSlider != null)
                    progressSlider.ValueChanged += ProgressSlider_ValueChanged;

                UpdateStatusBar();

                MessageBox.Show("🎬 مشغل الوسائط المتطور 2025 جاهز!\n✨ مميزات متطورة | AI Enhanced | 4K Support ✨",
                              "مرحباً - Welcome", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // دوال السحب والإفلات
        private void SimpleMainWindow_DragEnter(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent(DataFormats.FileDrop))
                e.Effects = DragDropEffects.Copy;
            else
                e.Effects = DragDropEffects.None;
        }

        private void SimpleMainWindow_Drop(object sender, DragEventArgs e)
        {
            try
            {
                string[] files = (string[])e.Data.GetData(DataFormats.FileDrop);
                if (files != null && files.Length > 0)
                {
                    LoadFiles(files);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الملفات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // اختصارات لوحة المفاتيح
        private void SimpleMainWindow_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.Key)
            {
                case Key.Space:
                    PlayPause_Click(sender, e);
                    break;
                case Key.S:
                    Stop_Click(sender, e);
                    break;
                case Key.F11:
                    Fullscreen_Click(sender, e);
                    break;
                case Key.O:
                    if (Keyboard.Modifiers == ModifierKeys.Control)
                        OpenFile_Click(sender, e);
                    break;
                case Key.Right:
                    Next_Click(sender, e);
                    break;
                case Key.Left:
                    Previous_Click(sender, e);
                    break;
                case Key.Up:
                    if (volumeSlider != null && volumeSlider.Value < 1.0)
                        volumeSlider.Value = Math.Min(1.0, volumeSlider.Value + 0.1);
                    break;
                case Key.Down:
                    if (volumeSlider != null && volumeSlider.Value > 0.0)
                        volumeSlider.Value = Math.Max(0.0, volumeSlider.Value - 0.1);
                    break;
            }
        }

        private void InitializeTimer()
        {
            timer = new DispatcherTimer();
            timer.Interval = TimeSpan.FromSeconds(1);
            timer.Tick += Timer_Tick;
        }

        private void Timer_Tick(object? sender, EventArgs e)
        {
            try
            {
                if (mediaPlayer?.Source != null && mediaPlayer.NaturalDuration.HasTimeSpan)
                {
                    var current = mediaPlayer.Position;
                    var total = mediaPlayer.NaturalDuration.TimeSpan;

                    if (currentTime != null)
                        currentTime.Text = FormatTime(current);

                    if (totalTime != null)
                        totalTime.Text = FormatTime(total);

                    if (progressSlider != null && total.TotalSeconds > 0)
                        progressSlider.Value = (current.TotalSeconds / total.TotalSeconds) * 100;
                }
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }

        private string FormatTime(TimeSpan time)
        {
            return $"{(int)time.TotalMinutes:D2}:{time.Seconds:D2}";
        }

        // دوال إدارة الملفات
        private void LoadFiles(string[] files)
        {
            try
            {
                var mediaFiles = files.Where(f => IsMediaFile(f)).ToList();
                if (mediaFiles.Any())
                {
                    playlist.Clear();
                    playlist.AddRange(mediaFiles);
                    currentTrackIndex = 0;
                    LoadCurrentTrack();
                    UpdateStatusBar();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الملفات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool IsMediaFile(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLower();
            return new[] { ".mp4", ".avi", ".wmv", ".mov", ".mkv", ".mp3", ".wav", ".wma", ".flac", ".m4a", ".webm", ".ogg" }
                   .Contains(extension);
        }

        private void LoadCurrentTrack()
        {
            if (playlist.Count > 0 && currentTrackIndex >= 0 && currentTrackIndex < playlist.Count)
            {
                try
                {
                    var filePath = playlist[currentTrackIndex];
                    if (mediaPlayer != null)
                    {
                        mediaPlayer.Source = new Uri(filePath);
                        Title = $"🎬 مشغل الوسائط المتطور 2025 - {Path.GetFileName(filePath)}";

                        if (fileInfoText != null)
                            fileInfoText.Text = $"({currentTrackIndex + 1}/{playlist.Count}) {Path.GetFileName(filePath)}";

                        if (welcomeScreen != null)
                            welcomeScreen.Visibility = Visibility.Collapsed;

                        if (videoOverlay != null)
                            videoOverlay.Visibility = Visibility.Visible;
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تحميل الملف: {ex.Message}", "خطأ",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void UpdateStatusBar()
        {
            try
            {
                if (statusText != null)
                    statusText.Text = isPlaying ? "يتم التشغيل - Playing" : "جاهز - Ready";

                if (playlistInfo != null)
                    playlistInfo.Text = $"قائمة التشغيل: {playlist.Count} ملف";

                if (qualityText != null)
                    qualityText.Text = "4K Ready";
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }

        private void OpenFile_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var dialog = new OpenFileDialog();
                dialog.Multiselect = true;
                dialog.Filter = "Media Files|*.mp4;*.avi;*.wmv;*.mov;*.mkv;*.mp3;*.wav;*.wma;*.flac;*.m4a;*.webm;*.ogg|" +
                               "Video Files|*.mp4;*.avi;*.wmv;*.mov;*.mkv;*.webm|" +
                               "Audio Files|*.mp3;*.wav;*.wma;*.flac;*.m4a;*.ogg|" +
                               "All Files|*.*";

                if (dialog.ShowDialog() == true)
                {
                    LoadFiles(dialog.FileNames);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح الملف: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // دوال التحكم المتطورة
        private void PlayPause_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (mediaPlayer?.Source == null)
                {
                    if (playlist.Count == 0)
                    {
                        MessageBox.Show("يرجى فتح ملف أولاً\nPlease open a file first", "تنبيه - Notice",
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                        return;
                    }
                    LoadCurrentTrack();
                }

                if (isPlaying)
                {
                    mediaPlayer?.Pause();
                    UpdatePlayButtons("▶️");
                    timer?.Stop();
                    isPlaying = false;
                }
                else
                {
                    mediaPlayer?.Play();
                    UpdatePlayButtons("⏸️");
                    timer?.Start();
                    isPlaying = true;
                }

                UpdateStatusBar();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التشغيل: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdatePlayButtons(string content)
        {
            try
            {
                if (playButton != null)
                    playButton.Content = content;
                if (overlayPlayButton != null)
                    overlayPlayButton.Content = content;
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }

        private void Previous_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (playlist.Count > 0)
                {
                    currentTrackIndex = currentTrackIndex > 0 ? currentTrackIndex - 1 : playlist.Count - 1;
                    LoadCurrentTrack();
                    if (isPlaying && mediaPlayer != null)
                        mediaPlayer.Play();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التنقل: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Next_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (playlist.Count > 0)
                {
                    if (isShuffle)
                    {
                        Random random = new Random();
                        currentTrackIndex = random.Next(0, playlist.Count);
                    }
                    else
                    {
                        currentTrackIndex = currentTrackIndex < playlist.Count - 1 ? currentTrackIndex + 1 : 0;
                    }
                    LoadCurrentTrack();
                    if (isPlaying && mediaPlayer != null)
                        mediaPlayer.Play();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التنقل: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Stop_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                mediaPlayer?.Stop();
                if (playButton != null)
                    playButton.Content = "▶️";
                timer?.Stop();
                isPlaying = false;
                if (progressSlider != null)
                    progressSlider.Value = 0;
                if (currentTime != null)
                    currentTime.Text = "00:00";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الإيقاف: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void VolumeSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            try
            {
                if (mediaPlayer != null && volumeSlider != null)
                {
                    mediaPlayer.Volume = volumeSlider.Value;
                    if (volumeText != null)
                        volumeText.Text = $"{(int)(volumeSlider.Value * 100)}%";
                }
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }

        private void ProgressSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            // لا نفعل شيء هنا لتجنب المشاكل
        }

        // دوال النافذة المتطورة
        private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (e.ClickCount == 2)
                    Maximize_Click(sender, e);
                else
                    this.DragMove();
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }

        private void Minimize_Click(object sender, RoutedEventArgs e)
        {
            this.WindowState = WindowState.Minimized;
        }

        private void Maximize_Click(object sender, RoutedEventArgs e)
        {
            this.WindowState = this.WindowState == WindowState.Maximized ?
                              WindowState.Normal : WindowState.Maximized;
        }

        private void Close_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void Fullscreen_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!isFullscreen)
                {
                    previousWindowState = this.WindowState;
                    previousWindowStyle = this.WindowStyle;

                    this.WindowStyle = WindowStyle.None;
                    this.WindowState = WindowState.Maximized;
                    this.Topmost = true;
                    isFullscreen = true;
                }
                else
                {
                    this.WindowStyle = previousWindowStyle;
                    this.WindowState = previousWindowState;
                    this.Topmost = false;
                    isFullscreen = false;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في ملء الشاشة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // دوال المميزات المتطورة
        private void Shuffle_Click(object sender, RoutedEventArgs e)
        {
            isShuffle = !isShuffle;
            MessageBox.Show(isShuffle ? "تم تفعيل الخلط العشوائي ✅" : "تم إلغاء الخلط العشوائي ❌",
                          "الخلط العشوائي - Shuffle", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void Repeat_Click(object sender, RoutedEventArgs e)
        {
            isRepeat = !isRepeat;
            MessageBox.Show(isRepeat ? "تم تفعيل التكرار ✅" : "تم إلغاء التكرار ❌",
                          "التكرار - Repeat", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void Favorite_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("⭐ تم إضافة الملف للمفضلة!\n⭐ Added to favorites!",
                          "المفضلة - Favorites", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void Playlist_Click(object sender, RoutedEventArgs e)
        {
            if (playlist.Count > 0)
            {
                string playlistText = "📋 قائمة التشغيل - Playlist:\n\n";
                for (int i = 0; i < playlist.Count; i++)
                {
                    string marker = i == currentTrackIndex ? "▶️ " : "   ";
                    playlistText += $"{marker}{i + 1}. {Path.GetFileName(playlist[i])}\n";
                }
                MessageBox.Show(playlistText, "قائمة التشغيل - Playlist",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            else
            {
                MessageBox.Show("📋 قائمة التشغيل فارغة\nPlaylist is empty",
                              "قائمة التشغيل - Playlist", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void Equalizer_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("🎵 المعادل قريباً!\n🎵 Equalizer coming soon!\n\n✨ مميزات متطورة في التحديث القادم",
                          "المعادل - Equalizer", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void Theme_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("🎨 تغيير المظهر قريباً!\n🎨 Theme changer coming soon!\n\n✨ مظاهر متعددة في التحديث القادم",
                          "المظهر - Theme", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void Subtitle_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("🎯 الترجمة قريباً!\n🎯 Subtitles coming soon!\n\n✨ دعم ترجمات متعددة",
                          "الترجمة - Subtitles", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void Filter_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("🎨 المرشحات قريباً!\n🎨 Filters coming soon!\n\n✨ مرشحات بصرية متطورة",
                          "المرشحات - Filters", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void Cast_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("📱 البث قريباً!\n📱 Cast coming soon!\n\n✨ بث لاسلكي للأجهزة الذكية",
                          "البث - Cast", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void Refresh_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (mediaPlayer?.Source != null)
                {
                    var currentSource = mediaPlayer.Source;
                    mediaPlayer.Source = null;
                    mediaPlayer.Source = currentSource;
                    MessageBox.Show("🔄 تم تحديث الملف بنجاح!", "تحديث - Refresh",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التحديث: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Settings_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("⚙️ الإعدادات قريباً!\n⚙️ Settings coming soon!\n\n✨ إعدادات متطورة ومخصصة",
                          "الإعدادات - Settings", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        // دوال الوسائط
        private void MediaPlayer_MediaOpened(object sender, RoutedEventArgs e)
        {
            try
            {
                if (mediaPlayer?.NaturalDuration.HasTimeSpan == true && totalTime != null)
                {
                    totalTime.Text = FormatTime(mediaPlayer.NaturalDuration.TimeSpan);
                }
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }

        private void MediaPlayer_MediaEnded(object sender, RoutedEventArgs e)
        {
            try
            {
                if (isRepeat)
                {
                    mediaPlayer?.Play();
                }
                else if (playlist.Count > 1)
                {
                    Next_Click(sender, e);
                }
                else
                {
                    Stop_Click(sender, e);
                }
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            timer?.Stop();
            mediaPlayer?.Close();
            base.OnClosed(e);
        }
    }
}
