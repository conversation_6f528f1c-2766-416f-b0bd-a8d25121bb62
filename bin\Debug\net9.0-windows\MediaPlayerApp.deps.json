{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"MediaPlayerApp/1.0.0": {"dependencies": {"LibVLCSharp": "3.8.0", "LibVLCSharp.WPF": "3.8.0", "VideoLAN.LibVLC.Windows": "3.0.18"}, "runtime": {"MediaPlayerApp.dll": {}}}, "LibVLCSharp/3.8.0": {"runtime": {"lib/net6.0/LibVLCSharp.dll": {"assemblyVersion": "3.8.0.0", "fileVersion": "3.8.0.0"}}}, "LibVLCSharp.WPF/3.8.0": {"dependencies": {"LibVLCSharp": "3.8.0"}, "runtime": {"lib/net6.0-windows7.0/LibVLCSharp.WPF.dll": {"assemblyVersion": "3.8.0.0", "fileVersion": "3.8.0.0"}}}, "VideoLAN.LibVLC.Windows/3.0.18": {}}}, "libraries": {"MediaPlayerApp/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "LibVLCSharp/3.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-vHHnH+nu3M8fb4jSt4Ad4aVOq7trW4ZBh85WFp7QaWPWDUX9SFVcgYc5IKOtyNh1IaBM/OmZV6NuvTz6eDv6tw==", "path": "libvlcsharp/3.8.0", "hashPath": "libvlcsharp.3.8.0.nupkg.sha512"}, "LibVLCSharp.WPF/3.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-6AaIfxhPYH5gh9GbFZtz7rjsDZpxx0XS7V94tGK8bkBZzE0ZUpFD/3Zo0lsVsZCMY7nTE+e0wBsVW5uAZYL7Dg==", "path": "libvlcsharp.wpf/3.8.0", "hashPath": "libvlcsharp.wpf.3.8.0.nupkg.sha512"}, "VideoLAN.LibVLC.Windows/3.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-A7Fg86aSziadDxB4F8AWN6kHNgEYJqerdPG9SflPM0GDO9m/O2jnc60mfSa52xOh5EC4U62W0y4kdyw+FgB2iQ==", "path": "videolan.libvlc.windows/3.0.18", "hashPath": "videolan.libvlc.windows.3.0.18.nupkg.sha512"}}}