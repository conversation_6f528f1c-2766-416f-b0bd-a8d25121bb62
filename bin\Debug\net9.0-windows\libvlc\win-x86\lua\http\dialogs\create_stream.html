<script type="text/javascript">
//<![CDATA[
	$(function(){
		$('#stream_out_method').change(function(){
			$('#output_options').empty();
			switch($(this).val()){
				case 'file':
					var options	=	$('#file_options').clone();
					break;
				case 'http':
					var options	=	$('#net_options').clone();
					break;
				case 'mmsh':
				case 'rtp':
				case 'udp':
					var options	=	$('#net_options').clone();
					$('#stream_out_file_',options).val('');
					break;
			}
			$('[id]',options).each(function(){
				$(this).attr('id',$(this).attr('id').substr(0,$(this).attr('id').length-1));
				$(this).attr('name',$(this).attr('name').substr(0,$(this).attr('name').length-1));
			});
			$(options).css({
				'visibility':'visible',
				'display':'block'
			})
			$(options).appendTo('#output_options');
		});
		$('#stream_out_mux').change(function(){
			if($(this).val()=='ffmpeg'){
				$('#stream_out_mux_opts').val('{mux=flv}');
			}else{
				$('#stream_out_mux_opts').val('');
			}
		});
		$('#window_create_stream').dialog({
			autoOpen: false,
			width:800,
			modal: true,
			buttons:{
				"<?vlc gettext("Create") ?>":function(){
					var e	=	false;
					$('input',$(this)).removeClass('ui-state-error');
					$('#stream_error_container').css({
						'visibility':'hidden',
						'display':'none'
					});
					if(!$('#stream_name').val()){
						$('#stream_name').addClass('ui-state-error');
						e	=	true;
					}
					if(!$('#stream_input').val()){
						$('#stream_input').addClass('ui-state-error');
						e	=	true;
					}

					if($('#stream_out_method').val()!='file' && !$('#stream_out_port').val()){
						$('#stream_out_port').addClass('ui-state-error');
						e	=	true;
					}
					if($('#stream_out_method').val()!='file' && !$('#stream_out_dest').val()){
						$('#stream_out_dest').addClass('ui-state-error');
						e	=	true;
					}

					if($('#stream_out_method').val()=='file' && !$('#stream_out_filename').val()){
						$('#stream_out_filename').addClass('ui-state-error');
						e	=	true;
					}
					if(e){
						$('#stream_error_message').empty();
						$('#stream_error_message').append('One or more fields require attention.');
						$('#stream_error_container').css({
							'visibility':'visible',
							'display':'block'
						})
					}else{
						sendVLMCmd(buildStreamCode());
						$(this).dialog('close');
					}
				},
				"<?vlc gettext("Cancel") ?>":function(){
					$(this).dialog('close');
				}
			}
		});
		$('#button_input').click(function(){
			browse_target	=	'#stream_input';
			browse();
			$('#window_browse').dialog('open');
		});
		$('#button_in_screen').click(function(){
			$('#stream_input').val('screen://');
		});
	});
	function buildStreamCode(){
		var name		=	$('#stream_name').val().replace(' ','_');
		var infile		=	$('#stream_input').val();

		var vcodec		=	$('#stream_vcodec').val();
		var vb			=	$('#stream_vb').val();
		var fps			=	$('#stream_fps').val();
		var scale		=	$('#stream_scale').val();
		var dlace		=	$('#stream_deinterlace').is(':checked');

		var acodec		=	$('#stream_acodec').val();
		var ab			=	$('#stream_ab').val();
		var srate		=	$('#stream_samplerate').val();
		var channels	=	$('#stream_channels').val();

		var scodec		=	$('#stream_scodec').val() && !$('#stream_soverlay').checked ? ','+$('#stream_scodec').val() : '';
		var soverlay	=	$('#stream_soverlay').is(':checked') ? ',soverlay' : '';

		var outmethod	=	$('#stream_out_method').val();
		var mux			=	$('#stream_out_mux').val();
		var muxoptions	=	$('#stream_out_mux_opts').val() ? '{'+$('#stream_out_mux_opts').val()+'}' : '';

		if(outmethod=='file'){
			var filename	=	$('#stream_out_filename').val();
		}else{
			var outport		=	$('#stream_out_port').val();
			var outdest		=	$('#stream_out_dest').val();
			var outfile		=	$('#stream_out_file').val();
		}
		var dest		=	outmethod=='file' ? filename : (outfile ? outdest+':'+outport+'/'+outfile : outdest+':'+outport);
		var inCode		=	'new '+name+' broadcast enabled input "'+infile+'" ';
		var transCode	=	'output #transcode{vcodec='+vcodec+',vb='+vb+',fps='+fps+',scale='+scale+',acodec='+acodec+',ab='+ab+',samplerate='+srate+',channels='+channels+scodec+soverlay+'}';
		var outCode		=	':std{access='+outmethod+',mux='+mux+muxoptions+',dst='+dest+'}';

		return inCode+transCode+outCode;
	}
//]]>
</script>
<div id="window_create_stream" title="<?vlc gettext("Create Stream") ?>">
	<table width="100%">
		<tr>
			<td style="text-align:right" valign="top">
				<h5><?vlc gettext("Stream name") ?></h5>
			</td>
			<td colspan="5" valign="top">
				<input type="text" name="stream_name" id="stream_name" value=""/>
			</td>
		</tr>
		<tr>
			<th colspan="2" valign="top">
				<h5><?vlc gettext("Video") ?></h5>
			</th>
			<th colspan="2" valign="top">
				<h5><?vlc gettext("Audio") ?></h5>
			</th>
			<th colspan="2" valign="top">
				<h5><?vlc gettext("Subtitles") ?></h5>
			</th>
			<th colspan="2" valign="top">
				<h5><?vlc gettext("Output") ?></h5>
			</th>
		</tr>
		<tr>
			<td style="text-align:right" valign="top"><?vlc gettext("Video codec") ?></td>
			<td valign="top">
				<select name="stream_vcodec" id="stream_vcodec">
					<option value="FLV1">FLV1</option>
					<option value="mp1v">mp1v</option>
					<option value="mp2v">mp2v</option>
					<option value="mp4v">mp4v</option>
					<option value="DIV1">DIV1</option>
					<option value="DIV2">DIV2</option>
					<option value="DIV3">DIV3</option>
					<option value="h263">H263</option>
					<option value="h264">H264</option>
					<option value="WMV1">WMV1</option>
					<option value="WMV2">WMV2</option>
					<option value="MJPG">MJPG</option>
					<option value="theo">theo</option>
				</select>
			</td>
			<td style="text-align:right" valign="top"><?vlc gettext("Audio codec") ?></td>
			<td valign="top">
				<select name="stream_acodec" id="stream_acodec">
					<option value="mp3">mp3</option>
					<option value="mpga">mpga</option>
					<option value="mp2a">mp2a</option>
					<option value="mp4a">mp4a</option>
					<option value="a52">a52</option>
					<option value="vorb">vorb</option>
					<option value="flac">flac</option>
					<option value="spx">spx</option>
					<option value="s16l">s16l</option>
					<option value="fl32">fl32</option>
				</select>
			</td>
			<td style="text-align:right" valign="top"><?vlc gettext("Subtitle codec") ?></td>
			<td valign="top">
				<select name="stream_scodec" id="stream_scodec">
					<option value=""><?vlc gettext("None") ?></option>
					<option value="dvbs">dvbs</option>
				</select>
			</td>
			<td style="text-align:right" valign="top"><?vlc gettext("Output	method") ?></td>
			<td valign="top">
				<select name="stream_out_method" id="stream_out_method">
					<option value="http">HTTP</option>
					<option value="file"><?vlc gettext("File") ?></option>
					<option value="mmsh">MMSH</option>
					<option value="rtp">RTP</option>
					<option value="udp">UDP</option>
				</select>
			</td>
		</tr>
		<tr>
			<td style="text-align:right" valign="top"><?vlc gettext("Video bitrate") ?></td>
			<td valign="top">
				<select name="stream_vb" id="stream_vb">
					<option value="4096">4096</option>
					<option value="3072">3072</option>
					<option value="2048">2048</option>
					<option value="1024">1024</option>
					<option value="768">768</option>
					<option value="512">512</option>
					<option value="384">384</option>
					<option value="256">256</option>
					<option value="192">192</option>
					<option value="128">128</option>
					<option value="96">96</option>
					<option value="64">64</option>
					<option value="32">32</option>
					<option value="16">16</option>
				</select>
			</td>
			<td style="text-align:right" valign="top"><?vlc gettext("Audio bitrate") ?></td>
			<td valign="top">
				<select name="stream_ab" id="stream_ab">
					<option value="512">512</option>
					<option value="384">384</option>
					<option value="256">256</option>
					<option value="192">192</option>
					<option value="128">128</option>
					<option value="96">96</option>
					<option value="64">64</option>
					<option value="32">32</option>
					<option value="16">16</option>
				</select>
			</td>
			<td style="text-align:right" valign="top"><?vlc gettext("Overlay") ?></td>
			<td valign="top">
				<input type="checkbox" name="stream_soverlay" id="stream_soverlay" value="1" />
			</td>
			<td style="text-align:right" valign="top"><?vlc gettext("Multiplexer") ?></td>
			<td valign="top">
				<select name="stream_out_mux" id="stream_out_mux">
					<option value="ts">MPEG TS</option>
					<option value="ps">MPEG PS</option>
					<option value="mpeg1">MPEG 1</option>
					<option value="ogg">OGG</option>
					<option value="asf">ASF</option>
					<option value="mp4">MP4</option>
					<option value="mov">MOV</option>
					<option value="wav">WAV</option>
					<option value="raw">Raw</option>
					<option value="ffmpeg" selected="selected">FFMPEG</option>
				</select>
			</td>
		</tr>
		<tr>
			<td style="text-align:right" valign="top"><?vlc gettext("Video FPS") ?></td>
			<td valign="top">
				<select name="stream_fps" id="stream_fps">
					<option value="300">300</option>
					<option value="120">120</option>
					<option value="100">100</option>
					<option value="72">72</option>
					<option value="60">60</option>
					<option value="50">50</option>
					<option value="48">48</option>
					<option value="30">30</option>
					<option value="25" selected="selected">25</option>
					<option value="24">24</option>
				</select>
			</td>
			<td style="text-align:right" valign="top"><?vlc gettext("Audio sample rate") ?></td>
			<td valign="top">
				<select name="stream_samplerate" id="stream_samplerate">
					<option value="192000">192 KHz</option>
					<option value="96000">96 KHz</option>
					<option value="50000">50 KHz</option>
					<option value="48000">48 KHz</option>
					<option value="44100" selected="selected">44 KHz</option>
					<option value="32000">32 KHz</option>
					<option value="22050">22 KHz</option>
					<option value="16000">16 KHz</option>
					<option value="11025">11 KHz</option>
					<option value="8000">8 KHz</option>
				</select>
			</td>
			<td colspan="2" valign="top">&nbsp;</td>
			<td style="text-align:right" valign="top"><?vlc gettext("MUX options") ?></td>
			<td valign="top">
				<input type="text" name="stream_out_mux_opts" id="stream_out_mux_opts" value="{mux=flv}" />
			</td>
		</tr>
		<tr>
			<td style="text-align:right" valign="top"><?vlc gettext("Video scale") ?></td>
			<td valign="top">
				<select name="stream_scale" id="stream_scale">
					<option value="0.25">25%</option>
					<option value="0.5">50%</option>
					<option value="0.75">75%</option>
					<option selected="selected" value="1">100%</option>
					<option value="1.25">125%</option>
					<option value="1.5">150%</option>
					<option value="1.75">175%</option>
					<option value="2">200%</option>
				</select>
			</td>
			<td style="text-align:right" valign="top"><?vlc gettext("Audio channels") ?></td>
			<td valign="top">
				<select name="stream_channels" id="stream_channels" >
					<option value="1">1</option>
					<option value="2" selected="selected">2</option>
					<option value="4">4</option>
					<option value="6">6</option>
				</select>
			</td>
			<td colspan="2" valign="top">&nbsp;</td>
			<td colspan="2" rowspan="2" valign="top">
				<div id="output_options">
						<table>
							<tr>
								<td style="text-align:right" valign="top"><?vlc gettext("Output port") ?></td>
								<td valign="top"><input type="text" name="stream_out_port" id="stream_out_port" value="8081" /></td>
							</tr>
							<tr>
								<td style="text-align:right" valign="top"><?vlc gettext("Output destination") ?></td>
								<td><input type="text" name="stream_out_dest" id="stream_out_dest" value="0.0.0.0" /></td>
							</tr>
							<tr>
								<td style="text-align:right" valign="top"><?vlc gettext("Output	file") ?></td>
								<td valign="top"><input type="text" name="stream_out_file" id="stream_out_file" value="stream.flv" /></td>
							</tr>
						</table>
				</div>
			</td>
		</tr>
		<tr>
			<td valign="top" style="text-align:right"><?vlc gettext("Deinterlace") ?></td>
			<td valign="top">
				<input type="checkbox" name="stream_deinterlace" id="stream_deinterlace" value="1" />
			</td>
			<td colspan="2" valign="top">&nbsp;</td>
			<td colspan="2" valign="top">&nbsp;</td>
		</tr>
		<tr>
			<td style="text-align:right" colspan="2" valign="top">
				<?vlc gettext("Input media") ?>
			</td>
			<td colspan="6" valign="top">
				<input type="text" name="stream_input" id="stream_input" value="" size="50" />
				<div id="button_input" class="button icon ui-widget ui-state-default" title="<?vlc gettext("Media file") ?>" opendialog="window_browse"><span class="ui-icon ui-icon-eject"></span></div>
				<div id="button_in_screen" class="button icon ui-widget ui-state-default" title="<?vlc gettext("Capture screen") ?>" ><span class="ui-icon ui-icon-contact"></span></div>
			</td>
		</tr>
	</table>
	<div class="ui-widget" id="stream_error_container" style="display:none;visibility: hidden;">
		<div class="ui-state-error ui-corner-all" style="padding: 0 .7em;">
			<p><span class="ui-icon ui-icon-alert" style="float: left; margin-right: .3em;"></span>
			<strong><?vlc gettext("Error:") ?></strong> <span id="stream_error_message"><?vlc gettext("Sample ui-state-error style.") ?></span></p>
		</div>
	</div>
</div>
<div id="net_options" style="display:none;visibility: hidden;">
	<table>
		<tr>
			<td style="text-align:right" valign="top"><?vlc gettext("Output port") ?></td>
			<td valign="top"><input type="text" name="stream_out_port_" id="stream_out_port_" value="8081" /></td>
		</tr>
		<tr>
			<td style="text-align:right" valign="top"><?vlc gettext("Output destination") ?></td>
			<td valign="top"><input type="text" name="stream_out_dest_" id="stream_out_dest_" value="0.0.0.0" /></td>
		</tr>
		<tr>
			<td style="text-align:right" valign="top"><?vlc gettext("Output file") ?></td>
			<td valign="top"><input type="text" name="stream_out_file_" id="stream_out_file_" value="stream.flv" /></td>
		</tr>
	</table>
</div>
<div id="file_options" style="display:none;visibility: hidden;">
	<table>
		<tr>
			<td style="text-align:right" valign="top"><?vlc gettext("File name") ?></td>
			<td valign="top"><input type="text" name="stream_out_filename_" id="stream_out_filename_"/></td>
		</tr>
	</table>
</div>
