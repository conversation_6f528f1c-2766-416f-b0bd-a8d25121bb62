using System;
using System.Windows;

namespace MediaPlayerApp
{
    public partial class SettingsWindow : Window
    {
        public SettingsWindow()
        {
            InitializeComponent();
            LoadSettings();
        }

        private void LoadSettings()
        {
            try
            {
                // تحميل الإعدادات المحفوظة (يمكن تطويرها لاحق<|im_start|> لحفظ الإعدادات في ملف)
                
                // إعدادات الفيديو
                HardwareAcceleration.IsChecked = true;
                AutoResize.IsChecked = false;
                KeepAspectRatio.IsChecked = true;
                VideoQuality.SelectedIndex = 1;

                // إعدادات الصوت
                AudioEnhancement.IsChecked = true;
                SurroundSound.IsChecked = false;
                DefaultVolume.Value = 50;
                AudioDevice.SelectedIndex = 0;

                // إعدادات الترجمة
                AutoLoadSubtitles.IsChecked = true;
                SubtitleSync.IsChecked = true;
                SubtitleFontSize.Value = 18;
                SubtitleColor.SelectedIndex = 0;

                // إعدادات عامة
                StartWithWindows.IsChecked = false;
                RememberPosition.IsChecked = true;
                AutoUpdate.IsChecked = true;
                ShowNotifications.IsChecked = true;
                Language.SelectedIndex = 0;

                // إعدادات الأداء
                MemoryUsage.Value = 5;
                PreviewQuality.SelectedIndex = 1;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإعدادات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SaveSettings()
        {
            try
            {
                // حفظ الإعدادات (يمكن تطويرها لاحق<|im_start|> لحفظ الإعدادات في ملف)
                
                // هنا يمكن إضافة كود لحفظ الإعدادات في ملف JSON أو Registry
                
                MessageBox.Show("تم حفظ الإعدادات بنجاح! ✅", "نجح الحفظ",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ResetSettings()
        {
            try
            {
                var result = MessageBox.Show("هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟",
                                           "تأكيد إعادة التعيين",
                                           MessageBoxButton.YesNo,
                                           MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // إعادة تعيين جميع الإعدادات
                    
                    // إعدادات الفيديو
                    HardwareAcceleration.IsChecked = true;
                    AutoResize.IsChecked = false;
                    KeepAspectRatio.IsChecked = true;
                    VideoQuality.SelectedIndex = 1;

                    // إعدادات الصوت
                    AudioEnhancement.IsChecked = true;
                    SurroundSound.IsChecked = false;
                    DefaultVolume.Value = 50;
                    AudioDevice.SelectedIndex = 0;

                    // إعدادات الترجمة
                    AutoLoadSubtitles.IsChecked = true;
                    SubtitleSync.IsChecked = true;
                    SubtitleFontSize.Value = 18;
                    SubtitleColor.SelectedIndex = 0;

                    // إعدادات عامة
                    StartWithWindows.IsChecked = false;
                    RememberPosition.IsChecked = true;
                    AutoUpdate.IsChecked = true;
                    ShowNotifications.IsChecked = true;
                    Language.SelectedIndex = 0;

                    // إعدادات الأداء
                    MemoryUsage.Value = 5;
                    PreviewQuality.SelectedIndex = 1;

                    MessageBox.Show("تم إعادة تعيين الإعدادات بنجاح! 🔄", "تم إعادة التعيين",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعادة تعيين الإعدادات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // أحداث الأزرار
        private void CloseBtn_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void SaveBtn_Click(object sender, RoutedEventArgs e)
        {
            SaveSettings();
            this.Close();
        }

        private void CancelBtn_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void ResetBtn_Click(object sender, RoutedEventArgs e)
        {
            ResetSettings();
        }

        // إضافة وظائف للحصول على قيم الإعدادات
        public bool GetHardwareAcceleration() => HardwareAcceleration.IsChecked ?? true;
        public bool GetAutoResize() => AutoResize.IsChecked ?? false;
        public bool GetKeepAspectRatio() => KeepAspectRatio.IsChecked ?? true;
        public int GetVideoQuality() => VideoQuality.SelectedIndex;

        public bool GetAudioEnhancement() => AudioEnhancement.IsChecked ?? true;
        public bool GetSurroundSound() => SurroundSound.IsChecked ?? false;
        public double GetDefaultVolume() => DefaultVolume.Value;
        public int GetAudioDevice() => AudioDevice.SelectedIndex;

        public bool GetAutoLoadSubtitles() => AutoLoadSubtitles.IsChecked ?? true;
        public bool GetSubtitleSync() => SubtitleSync.IsChecked ?? true;
        public double GetSubtitleFontSize() => SubtitleFontSize.Value;
        public int GetSubtitleColor() => SubtitleColor.SelectedIndex;

        public bool GetStartWithWindows() => StartWithWindows.IsChecked ?? false;
        public bool GetRememberPosition() => RememberPosition.IsChecked ?? true;
        public bool GetAutoUpdate() => AutoUpdate.IsChecked ?? true;
        public bool GetShowNotifications() => ShowNotifications.IsChecked ?? true;
        public int GetLanguage() => Language.SelectedIndex;

        public double GetMemoryUsage() => MemoryUsage.Value;
        public int GetPreviewQuality() => PreviewQuality.SelectedIndex;
    }
}
