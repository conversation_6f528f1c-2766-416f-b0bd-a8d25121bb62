@echo off
echo تشغيل مشغل الوسائط...
echo Starting Media Player...
echo.
echo تحقق من وجود الملف...
if exist "bin\Debug\net9.0-windows\MediaPlayerApp.exe" (
    echo الملف موجود، جاري التشغيل...
    echo File exists, starting...
    "bin\Debug\net9.0-windows\MediaPlayerApp.exe"
    if errorlevel 1 (
        echo خطأ في التشغيل!
        echo Error running the application!
        pause
    )
) else (
    echo الملف غير موجود!
    echo File not found!
    echo يرجى بناء المشروع أولاً باستخدام: dotnet build
    echo Please build the project first using: dotnet build
    pause
)
