<script type="text/javascript">
//<![CDATA[
	$(function(){
		$('#window_offset').dialog({
			autoOpen: false,
			minWidth: 400,
			buttons:{
				"Close":function(){
					$(this).dialog("close");
				}
			}
		});
		$( "#rateSlider" ).slider({
			range: "min",
			value: 1,
			min: 0.25,
			max: 10,
			step: 0.25,
			stop: function( event, ui ) {
				sendCommand({
					'command':'rate',
					'val':(ui.value)
				})
			},
			slide: function(event,ui){
				$('#currentRate').empty();
				$('#currentRate').append(ui.value+'x');
			}
		});
		$( "#audioSlider" ).slider({
			range: "min",
			value: 0,
			min: -10,
			max: 10,
			step: 0.25,
			stop: function( event, ui ) {
				sendCommand({
					'command':'audiodelay',
					'val':(ui.value)
				})
			},
			slide: function(event,ui){
				$('#currentAudioDelay').empty();
				$('#currentAudioDelay').append(ui.value+'s');
			}
		});
		$( "#subtitleSlider" ).slider({
			range: "min",
			value: 0,
			min: -10,
			max: 10,
			step: 0.25,
			stop: function( event, ui ) {
				sendCommand({
					'command':'subdelay',
					'val':(ui.value)
				})
			},
			slide: function(event,ui){
				$('#currentSubtitleDelay').empty();
				$('#currentSubtitleDelay').append(ui.value+'s');
			}
		});
	});
//]]>
</script>
<div id="window_offset" title="<?vlc gettext("Track Synchronisation") ?>">
	<div><?vlc gettext("Playback Rate") ?></div>
	<div id="rateSlider" title="<?vlc gettext("Playback Rate") ?>"></div>
	<div id="currentRate" class="dynamic">1x</div>
	<br/>
	<div><?vlc gettext("Audio Delay") ?></div>
	<div id="audioSlider" title="<?vlc gettext("Audio Delay") ?>"></div>
	<div id="currentAudioDelay" class="dynamic">0s</div>
	<br/>
	<div><?vlc gettext("Subtitle Delay") ?></div>
	<div id="subtitleSlider" title="<?vlc gettext("Subtitle Delay") ?>"></div>
	<div id="currentSubtitleDelay" class="dynamic">0s</div>
</div>
