using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using Microsoft.Win32;

namespace MediaPlayerApp
{
    public partial class PlaylistWindow : Window
    {
        public ObservableCollection<PlaylistItem> PlaylistItems { get; set; }
        private List<PlaylistItem> allItems = new List<PlaylistItem>();
        public event EventHandler<PlaylistItem>? ItemSelected;

        public PlaylistWindow()
        {
            InitializeComponent();
            PlaylistItems = new ObservableCollection<PlaylistItem>();
            PlaylistBox.ItemsSource = PlaylistItems;
            UpdatePlaylistCount();
        }

        public void LoadPlaylist(List<string> files)
        {
            try
            {
                PlaylistItems.Clear();
                allItems.Clear();

                foreach (var file in files)
                {
                    if (File.Exists(file))
                    {
                        var item = new PlaylistItem
                        {
                            FilePath = file,
                            FileName = Path.GetFileName(file),
                            FileInfo = GetFileInfo(file),
                            Duration = GetFileDuration(file)
                        };
                        
                        PlaylistItems.Add(item);
                        allItems.Add(item);
                    }
                }

                UpdatePlaylistCount();
                UpdateEmptyState();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل قائمة التشغيل: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private string GetFileInfo(string filePath)
        {
            try
            {
                var fileInfo = new FileInfo(filePath);
                var size = FormatFileSize(fileInfo.Length);
                var extension = Path.GetExtension(filePath).ToUpper();
                return $"{extension} • {size}";
            }
            catch
            {
                return "معلومات غير متاحة";
            }
        }

        private string GetFileDuration(string filePath)
        {
            // هذه دالة مبسطة - يمكن تطويرها لاحق<|im_start|> لقراءة مدة الملف الفعلية
            return "--:--";
        }

        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        private void UpdatePlaylistCount()
        {
            PlaylistCount.Text = $"({PlaylistItems.Count} ملف)";
        }

        private void UpdateEmptyState()
        {
            EmptyPlaylist.Visibility = PlaylistItems.Count == 0 ? Visibility.Visible : Visibility.Collapsed;
        }

        // أحداث النافذة
        private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ClickCount == 1)
            {
                this.DragMove();
            }
        }

        private void CloseBtn_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        // أحداث البحث
        private void SearchBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (SearchBox.Text == "البحث في قائمة التشغيل...")
            {
                SearchBox.Text = "";
                SearchBox.Foreground = System.Windows.Media.Brushes.White;
            }
        }

        private void SearchBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(SearchBox.Text))
            {
                SearchBox.Text = "البحث في قائمة التشغيل...";
                SearchBox.Foreground = System.Windows.Media.Brushes.Gray;
            }
        }

        private void SearchBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            FilterPlaylist();
        }

        private void ClearSearchBtn_Click(object sender, RoutedEventArgs e)
        {
            SearchBox.Text = "";
            FilterPlaylist();
        }

        private void FilterPlaylist()
        {
            try
            {
                var searchText = SearchBox.Text;
                if (searchText == "البحث في قائمة التشغيل..." || string.IsNullOrWhiteSpace(searchText))
                {
                    // إظهار جميع العناصر
                    PlaylistItems.Clear();
                    foreach (var item in allItems)
                    {
                        PlaylistItems.Add(item);
                    }
                }
                else
                {
                    // تصفية العناصر
                    var filteredItems = allItems.Where(item => 
                        item.FileName.ToLower().Contains(searchText.ToLower()) ||
                        item.FileInfo.ToLower().Contains(searchText.ToLower())
                    ).ToList();

                    PlaylistItems.Clear();
                    foreach (var item in filteredItems)
                    {
                        PlaylistItems.Add(item);
                    }
                }

                UpdatePlaylistCount();
                UpdateEmptyState();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // أحداث قائمة التشغيل
        private void PlaylistBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // يمكن إضافة منطق هنا عند تغيير التحديد
        }

        private void PlaylistBox_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (PlaylistBox.SelectedItem is PlaylistItem selectedItem)
            {
                ItemSelected?.Invoke(this, selectedItem);
            }
        }

        // أحداث أزرار التحكم
        private void AddFilesBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var openFileDialog = new OpenFileDialog
                {
                    Multiselect = true,
                    Filter = "جميع ملفات الوسائط|*.mp4;*.avi;*.wmv;*.mov;*.mkv;*.mp3;*.wav;*.wma;*.flac;*.m4a;*.m4v;*.3gp;*.webm;*.ogg;*.ts;*.mts;*.vob|" +
                            "ملفات الفيديو|*.mp4;*.avi;*.wmv;*.mov;*.mkv;*.m4v;*.3gp;*.webm;*.ts;*.mts;*.vob|" +
                            "ملفات الصوت|*.mp3;*.wav;*.wma;*.flac;*.m4a;*.ogg|" +
                            "جميع الملفات|*.*"
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    var currentFiles = allItems.Select(item => item.FilePath).ToList();
                    var newFiles = openFileDialog.FileNames.Where(file => !currentFiles.Contains(file)).ToList();
                    
                    if (newFiles.Any())
                    {
                        LoadPlaylist(currentFiles.Concat(newFiles).ToList());
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة الملفات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RemoveBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (PlaylistBox.SelectedItem is PlaylistItem selectedItem)
                {
                    PlaylistItems.Remove(selectedItem);
                    allItems.Remove(selectedItem);
                    UpdatePlaylistCount();
                    UpdateEmptyState();
                }
                else
                {
                    MessageBox.Show("يرجى تحديد ملف لحذفه", "تنبيه",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف الملف: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ClearAllBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show("هل أنت متأكد من حذف جميع الملفات من قائمة التشغيل؟",
                                           "تأكيد الحذف",
                                           MessageBoxButton.YesNo,
                                           MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    PlaylistItems.Clear();
                    allItems.Clear();
                    UpdatePlaylistCount();
                    UpdateEmptyState();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في مسح قائمة التشغيل: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ShuffleBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (allItems.Count > 1)
                {
                    var random = new Random();
                    var shuffledItems = allItems.OrderBy(x => random.Next()).ToList();
                    
                    allItems.Clear();
                    allItems.AddRange(shuffledItems);
                    
                    PlaylistItems.Clear();
                    foreach (var item in allItems)
                    {
                        PlaylistItems.Add(item);
                    }

                    MessageBox.Show("تم خلط قائمة التشغيل! 🔀", "تم الخلط",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("يجب أن تحتوي قائمة التشغيل على ملفين على الأقل للخلط", "تنبيه",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في خلط قائمة التشغيل: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SavePlaylistBtn_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("ميزة حفظ قائمة التشغيل ستكون متاحة قريب<|im_start|>", "قريب<|im_start|>",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void LoadPlaylistBtn_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("ميزة تحميل قائمة التشغيل ستكون متاحة قريب<|im_start|>", "قريب<|im_start|>",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        public List<string> GetPlaylistFiles()
        {
            return allItems.Select(item => item.FilePath).ToList();
        }

        public void SetCurrentItem(string filePath)
        {
            var item = allItems.FirstOrDefault(i => i.FilePath == filePath);
            if (item != null)
            {
                PlaylistBox.SelectedItem = item;
                PlaylistBox.ScrollIntoView(item);
            }
        }
    }

    public class PlaylistItem
    {
        public string FilePath { get; set; } = "";
        public string FileName { get; set; } = "";
        public string FileInfo { get; set; } = "";
        public string Duration { get; set; } = "";
    }
}
