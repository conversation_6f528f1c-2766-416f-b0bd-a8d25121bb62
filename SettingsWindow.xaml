<Window x:Class="MediaPlayerApp.SettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="⚙️ إعدادات QQPlayer" 
        Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        Background="#FF1A1A1A"
        WindowStyle="None"
        AllowsTransparency="True"
        ResizeMode="NoResize">

    <Window.Resources>
        <SolidColorBrush x:Key="PrimaryBlue" Color="#FF0078D4"/>
        <SolidColorBrush x:Key="DarkBackground" Color="#FF1A1A1A"/>
        <SolidColorBrush x:Key="ControlBackground" Color="#FF2D2D2D"/>
        <SolidColorBrush x:Key="HoverBlue" Color="#FF106EBE"/>
        
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource ControlBackground}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="5" Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource HoverBlue}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SettingsCheckBox" TargetType="CheckBox">
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Margin" Value="0,5"/>
        </Style>

        <Style x:Key="SettingsSlider" TargetType="Slider">
            <Setter Property="Background" Value="#FF404040"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryBlue}"/>
            <Setter Property="Height" Value="20"/>
        </Style>
    </Window.Resources>

    <Border BorderBrush="{StaticResource PrimaryBlue}" BorderThickness="2" CornerRadius="10">
        <Grid>
            <Grid.Background>
                <RadialGradientBrush>
                    <GradientStop Color="#FF1A1A1A" Offset="0"/>
                    <GradientStop Color="#FF2D2D2D" Offset="1"/>
                </RadialGradientBrush>
            </Grid.Background>

            <Grid.RowDefinitions>
                <RowDefinition Height="50"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="60"/>
            </Grid.RowDefinitions>

            <!-- شريط العنوان -->
            <Grid Grid.Row="0" Background="{StaticResource PrimaryBlue}">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" Margin="15,0" VerticalAlignment="Center">
                    <TextBlock Text="⚙️" FontSize="18" Margin="0,0,10,0"/>
                    <TextBlock Text="إعدادات QQPlayer" FontSize="16" FontWeight="Bold" Foreground="White"/>
                </StackPanel>

                <Button Grid.Column="1" x:Name="CloseBtn" Content="✕" Width="50" Height="50" 
                        Background="Transparent" Foreground="White" BorderThickness="0"
                        Click="CloseBtn_Click" FontSize="16"/>
            </Grid>

            <!-- المحتوى -->
            <ScrollViewer Grid.Row="1" Margin="20" VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <!-- إعدادات الفيديو -->
                    <GroupBox Header="🎬 إعدادات الفيديو" Foreground="White" Margin="0,0,0,20">
                        <GroupBox.HeaderTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding}" FontSize="14" FontWeight="Bold" 
                                          Foreground="{StaticResource PrimaryBlue}"/>
                            </DataTemplate>
                        </GroupBox.HeaderTemplate>
                        
                        <StackPanel Margin="10">
                            <CheckBox x:Name="HardwareAcceleration" Content="تسريع الأجهزة (Hardware Acceleration)" 
                                     Style="{StaticResource SettingsCheckBox}" IsChecked="True"/>
                            <CheckBox x:Name="AutoResize" Content="تغيير حجم النافذة تلقائياً" 
                                     Style="{StaticResource SettingsCheckBox}" IsChecked="False"/>
                            <CheckBox x:Name="KeepAspectRatio" Content="الحفاظ على نسبة العرض إلى الارتفاع" 
                                     Style="{StaticResource SettingsCheckBox}" IsChecked="True"/>
                            
                            <TextBlock Text="جودة العرض:" Foreground="White" Margin="0,10,0,5"/>
                            <ComboBox x:Name="VideoQuality" Background="{StaticResource ControlBackground}" 
                                     Foreground="White" SelectedIndex="1">
                                <ComboBoxItem Content="منخفضة"/>
                                <ComboBoxItem Content="متوسطة"/>
                                <ComboBoxItem Content="عالية"/>
                                <ComboBoxItem Content="فائقة الجودة"/>
                            </ComboBox>
                        </StackPanel>
                    </GroupBox>

                    <!-- إعدادات الصوت -->
                    <GroupBox Header="🎵 إعدادات الصوت" Foreground="White" Margin="0,0,0,20">
                        <GroupBox.HeaderTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding}" FontSize="14" FontWeight="Bold" 
                                          Foreground="{StaticResource PrimaryBlue}"/>
                            </DataTemplate>
                        </GroupBox.HeaderTemplate>
                        
                        <StackPanel Margin="10">
                            <CheckBox x:Name="AudioEnhancement" Content="تحسين الصوت" 
                                     Style="{StaticResource SettingsCheckBox}" IsChecked="True"/>
                            <CheckBox x:Name="SurroundSound" Content="صوت محيطي (Surround Sound)" 
                                     Style="{StaticResource SettingsCheckBox}" IsChecked="False"/>
                            
                            <TextBlock Text="مستوى الصوت الافتراضي:" Foreground="White" Margin="0,10,0,5"/>
                            <Slider x:Name="DefaultVolume" Style="{StaticResource SettingsSlider}" 
                                   Minimum="0" Maximum="100" Value="50" Margin="0,0,0,10"/>
                            
                            <TextBlock Text="جهاز الصوت:" Foreground="White" Margin="0,10,0,5"/>
                            <ComboBox x:Name="AudioDevice" Background="{StaticResource ControlBackground}" 
                                     Foreground="White" SelectedIndex="0">
                                <ComboBoxItem Content="الافتراضي"/>
                                <ComboBoxItem Content="سماعات الرأس"/>
                                <ComboBoxItem Content="مكبرات الصوت"/>
                            </ComboBox>
                        </StackPanel>
                    </GroupBox>

                    <!-- إعدادات الترجمة -->
                    <GroupBox Header="📝 إعدادات الترجمة" Foreground="White" Margin="0,0,0,20">
                        <GroupBox.HeaderTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding}" FontSize="14" FontWeight="Bold" 
                                          Foreground="{StaticResource PrimaryBlue}"/>
                            </DataTemplate>
                        </GroupBox.HeaderTemplate>
                        
                        <StackPanel Margin="10">
                            <CheckBox x:Name="AutoLoadSubtitles" Content="تحميل الترجمة تلقائياً" 
                                     Style="{StaticResource SettingsCheckBox}" IsChecked="True"/>
                            <CheckBox x:Name="SubtitleSync" Content="مزامنة الترجمة تلقائياً" 
                                     Style="{StaticResource SettingsCheckBox}" IsChecked="True"/>
                            
                            <TextBlock Text="حجم خط الترجمة:" Foreground="White" Margin="0,10,0,5"/>
                            <Slider x:Name="SubtitleFontSize" Style="{StaticResource SettingsSlider}" 
                                   Minimum="12" Maximum="36" Value="18" Margin="0,0,0,10"/>
                            
                            <TextBlock Text="لون الترجمة:" Foreground="White" Margin="0,10,0,5"/>
                            <ComboBox x:Name="SubtitleColor" Background="{StaticResource ControlBackground}" 
                                     Foreground="White" SelectedIndex="0">
                                <ComboBoxItem Content="أبيض"/>
                                <ComboBoxItem Content="أصفر"/>
                                <ComboBoxItem Content="أزرق"/>
                                <ComboBoxItem Content="أحمر"/>
                            </ComboBox>
                        </StackPanel>
                    </GroupBox>

                    <!-- إعدادات عامة -->
                    <GroupBox Header="🔧 إعدادات عامة" Foreground="White" Margin="0,0,0,20">
                        <GroupBox.HeaderTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding}" FontSize="14" FontWeight="Bold" 
                                          Foreground="{StaticResource PrimaryBlue}"/>
                            </DataTemplate>
                        </GroupBox.HeaderTemplate>
                        
                        <StackPanel Margin="10">
                            <CheckBox x:Name="StartWithWindows" Content="بدء التشغيل مع Windows" 
                                     Style="{StaticResource SettingsCheckBox}" IsChecked="False"/>
                            <CheckBox x:Name="RememberPosition" Content="تذكر موضع النافذة" 
                                     Style="{StaticResource SettingsCheckBox}" IsChecked="True"/>
                            <CheckBox x:Name="AutoUpdate" Content="التحديث التلقائي" 
                                     Style="{StaticResource SettingsCheckBox}" IsChecked="True"/>
                            <CheckBox x:Name="ShowNotifications" Content="إظهار الإشعارات" 
                                     Style="{StaticResource SettingsCheckBox}" IsChecked="True"/>
                            
                            <TextBlock Text="اللغة:" Foreground="White" Margin="0,10,0,5"/>
                            <ComboBox x:Name="Language" Background="{StaticResource ControlBackground}" 
                                     Foreground="White" SelectedIndex="0">
                                <ComboBoxItem Content="العربية"/>
                                <ComboBoxItem Content="English"/>
                                <ComboBoxItem Content="Français"/>
                                <ComboBoxItem Content="Español"/>
                            </ComboBox>
                        </StackPanel>
                    </GroupBox>

                    <!-- إعدادات الأداء -->
                    <GroupBox Header="⚡ إعدادات الأداء" Foreground="White" Margin="0,0,0,20">
                        <GroupBox.HeaderTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding}" FontSize="14" FontWeight="Bold" 
                                          Foreground="{StaticResource PrimaryBlue}"/>
                            </DataTemplate>
                        </GroupBox.HeaderTemplate>
                        
                        <StackPanel Margin="10">
                            <TextBlock Text="استخدام الذاكرة:" Foreground="White" Margin="0,0,0,5"/>
                            <Slider x:Name="MemoryUsage" Style="{StaticResource SettingsSlider}" 
                                   Minimum="1" Maximum="10" Value="5" Margin="0,0,0,10"/>
                            
                            <TextBlock Text="جودة المعاينة:" Foreground="White" Margin="0,0,0,5"/>
                            <ComboBox x:Name="PreviewQuality" Background="{StaticResource ControlBackground}" 
                                     Foreground="White" SelectedIndex="1">
                                <ComboBoxItem Content="منخفضة"/>
                                <ComboBoxItem Content="متوسطة"/>
                                <ComboBoxItem Content="عالية"/>
                            </ComboBox>
                        </StackPanel>
                    </GroupBox>
                </StackPanel>
            </ScrollViewer>

            <!-- أزرار التحكم -->
            <Grid Grid.Row="2" Background="#FF2D2D2D">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="20,10">
                    <Button x:Name="ResetBtn" Content="🔄 إعادة تعيين" Style="{StaticResource ModernButton}" 
                            Click="ResetBtn_Click" Margin="0,0,10,0"/>
                    <Button x:Name="SaveBtn" Content="💾 حفظ" Style="{StaticResource ModernButton}" 
                            Click="SaveBtn_Click" Margin="0,0,10,0" Background="{StaticResource PrimaryBlue}"/>
                    <Button x:Name="CancelBtn" Content="❌ إلغاء" Style="{StaticResource ModernButton}" 
                            Click="CancelBtn_Click"/>
                </StackPanel>
            </Grid>
        </Grid>
    </Border>
</Window>
