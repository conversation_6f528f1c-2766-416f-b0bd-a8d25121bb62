<?xml version="1.0" encoding="utf-8" standalone="yes" ?>
<?vlc --[[
vim:syntax=lua
<!--  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - >
<  vlm.xml: VLC media player web interface
< - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - >
<  Copyright (C) 2005-2006 the VideoLAN team
< 
<  Authors: <AUTHORS>
< 
<  This program is free software; you can redistribute it and/or modify
<  it under the terms of the GNU General Public License as published by
<  the Free Software Foundation; either version 2 of the License, or
<  (at your option) any later version.
< 
<  This program is distributed in the hope that it will be useful,
<  but WITHOUT ANY WARRANTY; without even the implied warranty of
<  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
<  GNU General Public License for more details.
< 
<  You should have received a copy of the GNU General Public License
<  along with this program; if not, write to the Free Software
<  Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston MA 02110-1301, USA.
< - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -->
]]

local function insert_children(c,t)
    if c.children then
      for _, d in ipairs(c.children) do
        table.insert(t,d.value or d.name)
      end
    end
end
local function print_table(name,t)
  print("<"..name.."s>")
  if #t ~= 0 then
    for _,v in ipairs(t) do
      print("<"..name..">")
        print(vlc.strings.convert_xml_special_chars(v))
      print("</"..name..">")
    end
  end
  print("</"..name.."s>")
end
local function print_media(m)
  local name = m.name
  local type_, enabled, output
  local loop = ""
  local inputs = {}
  local options = {}
  local instances = {}
  for _,c in ipairs(m.children) do
    if c.name=="type" then
      type_ = c.value
    elseif c.name=="enabled" then
      enabled = c.value
    elseif c.name=="loop" then
      loop = c.value
    elseif c.name=="output" then
      output = c.value
    elseif c.name=="inputs" then
      insert_children(c,inputs)
    elseif c.name=="options" then
      insert_children(c,options)
    elseif c.name=="instances" then
      if c.children then
        for _, d in ipairs(c.children) do
          local instance = "<instance "
          for _,e in ipairs(d.children) do
            instance = instance .. vlc.strings.convert_xml_special_chars(e.name) .. "=\"" .. vlc.strings.convert_xml_special_chars(e.value) .. "\" "
          end
          instance = instance .. "/>"
          table.insert(instances,instance)
        end
      end
    end
  end
  print("<"..type_.." name=\""..vlc.strings.convert_xml_special_chars(name).."\" enabled=\""..vlc.strings.convert_xml_special_chars(enabled).."\" loop=\""..vlc.strings.convert_xml_special_chars(loop).."\">\n")
  print("<output>"..vlc.strings.convert_xml_special_chars(output).."</output>\n")
  print_table("input",inputs)
  print_table("option",options)
  print "<instances>\n"
  if #instances ~= 0 then
    print(table.concat(instances))
  end
  print "</instances>\n"
  print("</"..type_..">\n")
end

local function print_schedule(m)
  local name = m.name
  local enabled, date, period, repeat_ = "", "", "", ""
  local commands = {}
  for _,c in ipairs(m.children) do
    if c.name=="enabled" then
      enabled = c.value
    elseif c.name=="date" then
      date = c.value
    elseif c.name=="period" then
      period = c.value
    elseif c.name=="repeat" then
      repeat_ = c.value
    elseif c.name=="commands" then
      insert_children(c,commands)
    end
  end
  print("<schedule name=\""..vlc.strings.convert_xml_special_chars(name).."\" enabled=\""..vlc.strings.convert_xml_special_chars(enabled).."\" period=\""..vlc.strings.convert_xml_special_chars(period).."\" repeat=\""..vlc.strings.convert_xml_special_chars(repeat_).."\">\n")
  print_table("command",commands)
  print("</schedule>\n")
end

local function print_xml(m)
  print "<vlm>"
  if m then
    for _, c in ipairs(m.children) do
      if c.name=="media" and c.children then
        for _, d in ipairs(c.children) do
          print_media(d)
        end
      elseif c.name=="schedule" and c.children then
        for _, d in ipairs(c.children) do
          print_schedule(d)
        end
      end
    end
  else
    print "oops"
  end
  print "</vlm>"
end

local function print_msg(m)
  if not m then return end
  print("<"..m.name..">\n")
  if m.children then
    for _, child in ipairs(m.children) do
      print_msg(child)
    end
  elseif m.value then
    print(m.value)
  end
  print("</"..m.name..">\n")
end

local msg = vlm:execute_command("show")
print_xml(msg)
--print_msg(msg)

?>
