﻿using System;
using System.Windows;

namespace MediaPlayerApp;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : System.Windows.Application
{
    protected override void OnStartup(StartupEventArgs e)
    {
        try
        {
            base.OnStartup(e);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في بدء التطبيق: {ex.Message}", "خطأ",
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void Application_DispatcherUnhandledException(object sender,
        System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
    {
        MessageBox.Show($"خطأ غير متوقع: {e.Exception.Message}", "خطأ",
                      MessageBoxButton.OK, MessageBoxImage.Error);
        e.Handled = true;
    }
}

