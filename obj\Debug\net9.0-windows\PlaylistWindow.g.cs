﻿#pragma checksum "..\..\..\PlaylistWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "BA995338F6B8DAA0E29B86E67B4629038E093BFB"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MediaPlayerApp {
    
    
    /// <summary>
    /// PlaylistWindow
    /// </summary>
    public partial class PlaylistWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 94 "..\..\..\PlaylistWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PlaylistCount;
        
        #line default
        #line hidden
        
        
        #line 98 "..\..\..\PlaylistWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseBtn;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\PlaylistWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchBox;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\PlaylistWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearSearchBtn;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\PlaylistWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox PlaylistBox;
        
        #line default
        #line hidden
        
        
        #line 169 "..\..\..\PlaylistWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel EmptyPlaylist;
        
        #line default
        #line hidden
        
        
        #line 190 "..\..\..\PlaylistWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddFilesBtn;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\PlaylistWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RemoveBtn;
        
        #line default
        #line hidden
        
        
        #line 194 "..\..\..\PlaylistWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearAllBtn;
        
        #line default
        #line hidden
        
        
        #line 200 "..\..\..\PlaylistWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ShuffleBtn;
        
        #line default
        #line hidden
        
        
        #line 202 "..\..\..\PlaylistWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SavePlaylistBtn;
        
        #line default
        #line hidden
        
        
        #line 204 "..\..\..\PlaylistWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LoadPlaylistBtn;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.4.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MediaPlayerApp;component/playlistwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\PlaylistWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.4.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 85 "..\..\..\PlaylistWindow.xaml"
            ((System.Windows.Controls.Grid)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.TitleBar_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 2:
            this.PlaylistCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.CloseBtn = ((System.Windows.Controls.Button)(target));
            
            #line 100 "..\..\..\PlaylistWindow.xaml"
            this.CloseBtn.Click += new System.Windows.RoutedEventHandler(this.CloseBtn_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.SearchBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 126 "..\..\..\PlaylistWindow.xaml"
            this.SearchBox.GotFocus += new System.Windows.RoutedEventHandler(this.SearchBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 126 "..\..\..\PlaylistWindow.xaml"
            this.SearchBox.LostFocus += new System.Windows.RoutedEventHandler(this.SearchBox_LostFocus);
            
            #line default
            #line hidden
            
            #line 127 "..\..\..\PlaylistWindow.xaml"
            this.SearchBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.ClearSearchBtn = ((System.Windows.Controls.Button)(target));
            
            #line 131 "..\..\..\PlaylistWindow.xaml"
            this.ClearSearchBtn.Click += new System.Windows.RoutedEventHandler(this.ClearSearchBtn_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.PlaylistBox = ((System.Windows.Controls.ListBox)(target));
            
            #line 139 "..\..\..\PlaylistWindow.xaml"
            this.PlaylistBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PlaylistBox_SelectionChanged);
            
            #line default
            #line hidden
            
            #line 140 "..\..\..\PlaylistWindow.xaml"
            this.PlaylistBox.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.PlaylistBox_MouseDoubleClick);
            
            #line default
            #line hidden
            return;
            case 7:
            this.EmptyPlaylist = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 8:
            this.AddFilesBtn = ((System.Windows.Controls.Button)(target));
            
            #line 191 "..\..\..\PlaylistWindow.xaml"
            this.AddFilesBtn.Click += new System.Windows.RoutedEventHandler(this.AddFilesBtn_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.RemoveBtn = ((System.Windows.Controls.Button)(target));
            
            #line 193 "..\..\..\PlaylistWindow.xaml"
            this.RemoveBtn.Click += new System.Windows.RoutedEventHandler(this.RemoveBtn_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.ClearAllBtn = ((System.Windows.Controls.Button)(target));
            
            #line 195 "..\..\..\PlaylistWindow.xaml"
            this.ClearAllBtn.Click += new System.Windows.RoutedEventHandler(this.ClearAllBtn_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.ShuffleBtn = ((System.Windows.Controls.Button)(target));
            
            #line 201 "..\..\..\PlaylistWindow.xaml"
            this.ShuffleBtn.Click += new System.Windows.RoutedEventHandler(this.ShuffleBtn_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.SavePlaylistBtn = ((System.Windows.Controls.Button)(target));
            
            #line 203 "..\..\..\PlaylistWindow.xaml"
            this.SavePlaylistBtn.Click += new System.Windows.RoutedEventHandler(this.SavePlaylistBtn_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.LoadPlaylistBtn = ((System.Windows.Controls.Button)(target));
            
            #line 205 "..\..\..\PlaylistWindow.xaml"
            this.LoadPlaylistBtn.Click += new System.Windows.RoutedEventHandler(this.LoadPlaylistBtn_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

