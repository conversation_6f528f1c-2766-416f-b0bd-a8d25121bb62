<?vlc --[[
vim:syntax=lua
<!--  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - >
<  playlist.json: VLC media player web interface
<  this should mirror the content of playlist.xml
< - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - >
<  Copyright (C) 2005-2006 the VideoLAN team
<
<  Authors: <AUTHORS>
<
<  This program is free software; you can redistribute it and/or modify
<  it under the terms of the GNU General Public License as published by
<  the Free Software Foundation; either version 2 of the License, or
<  (at your option) any later version.
<
<  This program is distributed in the hope that it will be useful,
<  but WITHOUT ANY WARRANTY; without even the implied warranty of
<  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
<  GNU General Public License for more details.
<
<  You should have received a copy of the GNU General Public License
<  along with this program; if not, write to the Free Software
<  Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston MA 02110-1301, USA.
< - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -->
]] ?>
<?vlc

--package.loaded.httprequests = nil --uncomment to debug changes
local httprequests = require "httprequests"

httprequests.processcommands()

httprequests.printTableAsJson(httprequests.playlisttable())

?>
