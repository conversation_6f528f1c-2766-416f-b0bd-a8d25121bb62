<!--  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - >
<  mobile_view.html: VLC media player web interface - VLM
< - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - >
<  Copyright (C) 2005-2014 VLC authors and VideoLAN
<
<  Authors: <AUTHORS>
<
<  This program is free software; you can redistribute it and/or modify
<  it under the terms of the GNU General Public License as published by
<  the Free Software Foundation; either version 2 of the License, or
<  (at your option) any later version.
<
<  This program is distributed in the hope that it will be useful,
<  but WITHOUT ANY WARRANTY; without even the implied warranty of
<  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
<  GNU General Public License for more details.
<
<  You should have received a copy of the GNU General Public License
<  along with this program; if not, write to the Free Software
<  Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston MA 02110-1301, USA.
< - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -->
<html>
	<head>
		<title><?vlc gettext("VLC media player - Web Interface") ?></title>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<meta name="referrer" content="no-referrer" />
		<meta name="viewport" content="width=device-width; user-scalable=no" />
		<link href="favicon.ico" type="image/x-icon" rel="shortcut icon"/>
		<script type="text/javascript" src="js/common.js"></script>
		<link type="text/css" href="css/ui-lightness/jquery-ui-1.8.13.custom.css" rel="stylesheet" />
		<link type="text/css" href="css/main.css" rel="stylesheet" />
		<script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.6.1/jquery.min.js"></script>
		<script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.8.13/jquery-ui.min.js"></script>
		<script type="text/javascript" src="js/jquery.jstree.js"></script>
		<script type="text/javascript" src="js/controllers.js"></script>
		<script type="text/javascript" src="js/ui.js"></script>
		<script type="text/javascript">
			var pollStatus	=	false;
			/* delay script loading so we won't block if we have no net access */
			$.getScript('https://releases.flowplayer.org/js/flowplayer-3.2.6.min.js', function(data, textStatus){
				$('#player').empty();
				$('#player').attr('href',$('#stream_protocol').val()+'://'+$('#stream_host').val()+':'+$('#stream_port').val()+'/'+$('#stream_file').val());
				flowplayer("player", "https://releases.flowplayer.org/swf/flowplayer-3.2.7.swf");
				/* .getScript only handles success() */
			});
		</script>
	</head>
	<body>
		<div id="player" style="width:100%;height:100%">

		</div>
		<?vlc
			dialogs("stream_config_window.html");
		?>
	</body>
</html>
