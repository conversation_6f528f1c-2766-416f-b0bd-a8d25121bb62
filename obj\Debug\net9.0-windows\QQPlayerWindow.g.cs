﻿#pragma checksum "..\..\..\QQPlayerWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "D3B12CF7238BE4FD1A6098A8C4EBA9300B30F2D4"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MediaPlayerApp {
    
    
    /// <summary>
    /// QQPlayerWindow
    /// </summary>
    public partial class QQPlayerWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 68 "..\..\..\QQPlayerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid TitleBar;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\QQPlayerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MinimizeButton;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\QQPlayerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MaximizeButton;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\QQPlayerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        
        #line 106 "..\..\..\QQPlayerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenFileBtn;
        
        #line default
        #line hidden
        
        
        #line 108 "..\..\..\QQPlayerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenFolderBtn;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\QQPlayerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SubtitlesBtn;
        
        #line default
        #line hidden
        
        
        #line 112 "..\..\..\QQPlayerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ScreenshotBtn;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\QQPlayerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SettingsBtn;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\QQPlayerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.MediaElement mediaPlayer;
        
        #line default
        #line hidden
        
        
        #line 130 "..\..\..\QQPlayerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid WelcomeScreen;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\QQPlayerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ControlsOverlay;
        
        #line default
        #line hidden
        
        
        #line 169 "..\..\..\QQPlayerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrevBtn;
        
        #line default
        #line hidden
        
        
        #line 171 "..\..\..\QQPlayerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PlayPauseBtn;
        
        #line default
        #line hidden
        
        
        #line 173 "..\..\..\QQPlayerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NextBtn;
        
        #line default
        #line hidden
        
        
        #line 194 "..\..\..\QQPlayerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentTimeText;
        
        #line default
        #line hidden
        
        
        #line 197 "..\..\..\QQPlayerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider ProgressSlider;
        
        #line default
        #line hidden
        
        
        #line 203 "..\..\..\QQPlayerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalTimeText;
        
        #line default
        #line hidden
        
        
        #line 217 "..\..\..\QQPlayerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PreviousBtn;
        
        #line default
        #line hidden
        
        
        #line 219 "..\..\..\QQPlayerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PlayPauseButton;
        
        #line default
        #line hidden
        
        
        #line 221 "..\..\..\QQPlayerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StopBtn;
        
        #line default
        #line hidden
        
        
        #line 223 "..\..\..\QQPlayerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NextButton;
        
        #line default
        #line hidden
        
        
        #line 229 "..\..\..\QQPlayerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileNameText;
        
        #line default
        #line hidden
        
        
        #line 231 "..\..\..\QQPlayerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileInfoText;
        
        #line default
        #line hidden
        
        
        #line 237 "..\..\..\QQPlayerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MuteBtn;
        
        #line default
        #line hidden
        
        
        #line 239 "..\..\..\QQPlayerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider VolumeSlider;
        
        #line default
        #line hidden
        
        
        #line 242 "..\..\..\QQPlayerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PlaylistBtn;
        
        #line default
        #line hidden
        
        
        #line 244 "..\..\..\QQPlayerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button FullscreenBtn;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.4.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MediaPlayerApp;component/qqplayerwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\QQPlayerWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.4.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TitleBar = ((System.Windows.Controls.Grid)(target));
            
            #line 69 "..\..\..\QQPlayerWindow.xaml"
            this.TitleBar.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.TitleBar_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 2:
            this.MinimizeButton = ((System.Windows.Controls.Button)(target));
            
            #line 86 "..\..\..\QQPlayerWindow.xaml"
            this.MinimizeButton.Click += new System.Windows.RoutedEventHandler(this.MinimizeButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.MaximizeButton = ((System.Windows.Controls.Button)(target));
            
            #line 88 "..\..\..\QQPlayerWindow.xaml"
            this.MaximizeButton.Click += new System.Windows.RoutedEventHandler(this.MaximizeButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 90 "..\..\..\QQPlayerWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.OpenFileBtn = ((System.Windows.Controls.Button)(target));
            
            #line 107 "..\..\..\QQPlayerWindow.xaml"
            this.OpenFileBtn.Click += new System.Windows.RoutedEventHandler(this.OpenFile_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.OpenFolderBtn = ((System.Windows.Controls.Button)(target));
            
            #line 109 "..\..\..\QQPlayerWindow.xaml"
            this.OpenFolderBtn.Click += new System.Windows.RoutedEventHandler(this.OpenFolder_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.SubtitlesBtn = ((System.Windows.Controls.Button)(target));
            
            #line 111 "..\..\..\QQPlayerWindow.xaml"
            this.SubtitlesBtn.Click += new System.Windows.RoutedEventHandler(this.Subtitles_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ScreenshotBtn = ((System.Windows.Controls.Button)(target));
            
            #line 113 "..\..\..\QQPlayerWindow.xaml"
            this.ScreenshotBtn.Click += new System.Windows.RoutedEventHandler(this.Screenshot_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.SettingsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 115 "..\..\..\QQPlayerWindow.xaml"
            this.SettingsBtn.Click += new System.Windows.RoutedEventHandler(this.Settings_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.mediaPlayer = ((System.Windows.Controls.MediaElement)(target));
            
            #line 126 "..\..\..\QQPlayerWindow.xaml"
            this.mediaPlayer.MediaOpened += new System.Windows.RoutedEventHandler(this.MediaPlayer_MediaOpened);
            
            #line default
            #line hidden
            
            #line 127 "..\..\..\QQPlayerWindow.xaml"
            this.mediaPlayer.MediaEnded += new System.Windows.RoutedEventHandler(this.MediaPlayer_MediaEnded);
            
            #line default
            #line hidden
            return;
            case 11:
            this.WelcomeScreen = ((System.Windows.Controls.Grid)(target));
            return;
            case 12:
            this.ControlsOverlay = ((System.Windows.Controls.Grid)(target));
            
            #line 166 "..\..\..\QQPlayerWindow.xaml"
            this.ControlsOverlay.MouseLeave += new System.Windows.Input.MouseEventHandler(this.ControlsOverlay_MouseLeave);
            
            #line default
            #line hidden
            return;
            case 13:
            this.PrevBtn = ((System.Windows.Controls.Button)(target));
            
            #line 170 "..\..\..\QQPlayerWindow.xaml"
            this.PrevBtn.Click += new System.Windows.RoutedEventHandler(this.Previous_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.PlayPauseBtn = ((System.Windows.Controls.Button)(target));
            
            #line 172 "..\..\..\QQPlayerWindow.xaml"
            this.PlayPauseBtn.Click += new System.Windows.RoutedEventHandler(this.PlayPause_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.NextBtn = ((System.Windows.Controls.Button)(target));
            
            #line 174 "..\..\..\QQPlayerWindow.xaml"
            this.NextBtn.Click += new System.Windows.RoutedEventHandler(this.Next_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.CurrentTimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.ProgressSlider = ((System.Windows.Controls.Slider)(target));
            
            #line 199 "..\..\..\QQPlayerWindow.xaml"
            this.ProgressSlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.ProgressSlider_ValueChanged);
            
            #line default
            #line hidden
            
            #line 200 "..\..\..\QQPlayerWindow.xaml"
            this.ProgressSlider.PreviewMouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.ProgressSlider_PreviewMouseLeftButtonDown);
            
            #line default
            #line hidden
            
            #line 201 "..\..\..\QQPlayerWindow.xaml"
            this.ProgressSlider.PreviewMouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.ProgressSlider_PreviewMouseLeftButtonUp);
            
            #line default
            #line hidden
            return;
            case 18:
            this.TotalTimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.PreviousBtn = ((System.Windows.Controls.Button)(target));
            
            #line 218 "..\..\..\QQPlayerWindow.xaml"
            this.PreviousBtn.Click += new System.Windows.RoutedEventHandler(this.Previous_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.PlayPauseButton = ((System.Windows.Controls.Button)(target));
            
            #line 220 "..\..\..\QQPlayerWindow.xaml"
            this.PlayPauseButton.Click += new System.Windows.RoutedEventHandler(this.PlayPause_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.StopBtn = ((System.Windows.Controls.Button)(target));
            
            #line 222 "..\..\..\QQPlayerWindow.xaml"
            this.StopBtn.Click += new System.Windows.RoutedEventHandler(this.Stop_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.NextButton = ((System.Windows.Controls.Button)(target));
            
            #line 224 "..\..\..\QQPlayerWindow.xaml"
            this.NextButton.Click += new System.Windows.RoutedEventHandler(this.Next_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.FileNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            this.FileInfoText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 25:
            this.MuteBtn = ((System.Windows.Controls.Button)(target));
            
            #line 238 "..\..\..\QQPlayerWindow.xaml"
            this.MuteBtn.Click += new System.Windows.RoutedEventHandler(this.Mute_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            this.VolumeSlider = ((System.Windows.Controls.Slider)(target));
            
            #line 241 "..\..\..\QQPlayerWindow.xaml"
            this.VolumeSlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.VolumeSlider_ValueChanged);
            
            #line default
            #line hidden
            return;
            case 27:
            this.PlaylistBtn = ((System.Windows.Controls.Button)(target));
            
            #line 243 "..\..\..\QQPlayerWindow.xaml"
            this.PlaylistBtn.Click += new System.Windows.RoutedEventHandler(this.Playlist_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            this.FullscreenBtn = ((System.Windows.Controls.Button)(target));
            
            #line 245 "..\..\..\QQPlayerWindow.xaml"
            this.FullscreenBtn.Click += new System.Windows.RoutedEventHandler(this.Fullscreen_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

