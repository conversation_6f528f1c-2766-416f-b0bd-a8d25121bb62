using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Threading;
using Microsoft.Win32;

namespace MediaPlayerApp;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    private DispatcherTimer? timer;
    private bool isDragging = false;
    private bool isPlaying = false;

    public MainWindow()
    {
        try
        {
            InitializeComponent();
            InitializeTimer();
            mediaPlayer.Volume = 0.5;

            // إظهار رسالة للتأكد من أن النافذة تعمل
            this.Loaded += MainWindow_Loaded;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تهيئة النافذة: {ex.Message}", "خطأ",
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void MainWindow_Loaded(object sender, RoutedEventArgs e)
    {
        // التأكد من أن النافذة ظاهرة
        this.WindowState = WindowState.Normal;
        this.Show();
        this.Activate();
        this.Focus();

        // رسالة للتأكد من أن التطبيق يعمل
        MessageBox.Show("مشغل الوسائط جاهز للاستخدام!\nMedia Player is ready to use!",
                       "مرحباً - Welcome",
                       MessageBoxButton.OK,
                       MessageBoxImage.Information);
    }

    private void InitializeTimer()
    {
        timer = new DispatcherTimer();
        timer.Interval = TimeSpan.FromSeconds(1);
        timer.Tick += Timer_Tick;
    }

    private void Timer_Tick(object? sender, EventArgs e)
    {
        if (mediaPlayer.Source != null && mediaPlayer.NaturalDuration.HasTimeSpan && !isDragging)
        {
            var currentTime = mediaPlayer.Position;
            var totalTime = mediaPlayer.NaturalDuration.TimeSpan;

            currentTimeText.Text = FormatTime(currentTime);
            totalTimeText.Text = FormatTime(totalTime);

            if (totalTime.TotalSeconds > 0)
            {
                progressSlider.Value = (currentTime.TotalSeconds / totalTime.TotalSeconds) * 100;
            }
        }
    }

    private string FormatTime(TimeSpan time)
    {
        return $"{(int)time.TotalMinutes:D2}:{time.Seconds:D2}";
    }

    private void OpenFile_Click(object sender, RoutedEventArgs e)
    {
        OpenFileDialog openFileDialog = new OpenFileDialog();
        openFileDialog.Filter = "Media Files|*.mp4;*.avi;*.wmv;*.mov;*.mkv;*.mp3;*.wav;*.wma|" +
                               "Video Files|*.mp4;*.avi;*.wmv;*.mov;*.mkv|" +
                               "Audio Files|*.mp3;*.wav;*.wma|" +
                               "All Files|*.*";

        if (openFileDialog.ShowDialog() == true)
        {
            try
            {
                mediaPlayer.Source = new Uri(openFileDialog.FileName);
                mediaPlayer.LoadedBehavior = MediaState.Manual;
                Title = $"مشغل الوسائط - {Path.GetFileName(openFileDialog.FileName)}";
                welcomeText.Visibility = Visibility.Collapsed;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح الملف: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    private void PlayPause_Click(object sender, RoutedEventArgs e)
    {
        if (mediaPlayer.Source == null)
        {
            MessageBox.Show("يرجى فتح ملف وسائط أولاً", "تنبيه",
                          MessageBoxButton.OK, MessageBoxImage.Information);
            return;
        }

        if (isPlaying)
        {
            mediaPlayer.Pause();
            playPauseButton.Content = "▶️";
            timer?.Stop();
            isPlaying = false;
        }
        else
        {
            mediaPlayer.Play();
            playPauseButton.Content = "⏸️";
            timer?.Start();
            isPlaying = true;
        }
    }

    private void Stop_Click(object sender, RoutedEventArgs e)
    {
        mediaPlayer.Stop();
        playPauseButton.Content = "▶️";
        timer?.Stop();
        isPlaying = false;
        progressSlider.Value = 0;
        currentTimeText.Text = "00:00";
    }

    private void Exit_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }

    private void MediaPlayer_MediaOpened(object sender, RoutedEventArgs e)
    {
        if (mediaPlayer.NaturalDuration.HasTimeSpan)
        {
            totalTimeText.Text = FormatTime(mediaPlayer.NaturalDuration.TimeSpan);
        }
    }

    private void MediaPlayer_MediaEnded(object sender, RoutedEventArgs e)
    {
        Stop_Click(sender, e);
    }

    private void ProgressSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
    {
        if (isDragging && mediaPlayer.NaturalDuration.HasTimeSpan)
        {
            var totalSeconds = mediaPlayer.NaturalDuration.TimeSpan.TotalSeconds;
            var newPosition = TimeSpan.FromSeconds((progressSlider.Value / 100) * totalSeconds);
            mediaPlayer.Position = newPosition;
        }
    }

    private void ProgressSlider_DragStarted(object sender, DragStartedEventArgs e)
    {
        isDragging = true;
    }

    private void ProgressSlider_DragCompleted(object sender, DragCompletedEventArgs e)
    {
        isDragging = false;
        if (mediaPlayer.NaturalDuration.HasTimeSpan)
        {
            var totalSeconds = mediaPlayer.NaturalDuration.TimeSpan.TotalSeconds;
            var newPosition = TimeSpan.FromSeconds((progressSlider.Value / 100) * totalSeconds);
            mediaPlayer.Position = newPosition;
        }
    }

    private void VolumeSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
    {
        if (mediaPlayer != null)
        {
            mediaPlayer.Volume = volumeSlider.Value;
            volumeText.Text = $"{(int)(volumeSlider.Value * 100)}%";
        }
    }

    protected override void OnClosed(EventArgs e)
    {
        timer?.Stop();
        mediaPlayer?.Close();
        base.OnClosed(e);
    }
}