﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Input;
using System.Windows.Threading;
using Microsoft.Win32;

namespace MediaPlayerApp;

/// <summary>
/// Advanced Media Player 2025 - مشغل الوسائط المتطور
/// </summary>
public partial class MainWindow : Window
{
    private DispatcherTimer? timer;
    private bool isDragging = false;
    private bool isPlaying = false;
    private bool isShuffleOn = false;
    private bool isRepeatOn = false;
    private List<string> playlist = new List<string>();
    private int currentTrackIndex = 0;
    private double savedVolume = 0.5;

    public MainWindow()
    {
        try
        {
            InitializeComponent();
            InitializeTimer();
            InitializeEventHandlers();

            // تهيئة إعدادات المشغل
            this.Loaded += MainWindow_Loaded;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تهيئة التطبيق: {ex.Message}", "خطأ",
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void InitializeEventHandlers()
    {
        // إضافة دعم السحب والإفلات
        this.AllowDrop = true;
        this.Drop += MainWindow_Drop;
        this.DragEnter += MainWindow_DragEnter;

        // إضافة اختصارات لوحة المفاتيح
        this.KeyDown += MainWindow_KeyDown;
    }

    private void MainWindow_Loaded(object sender, RoutedEventArgs e)
    {
        try
        {
            // تهيئة إعدادات المشغل
            if (mediaPlayer != null)
            {
                mediaPlayer.Volume = 0.5;
            }

            if (volumeSlider != null)
            {
                volumeSlider.Value = 0.5;
            }

            // تحديث واجهة المستخدم
            UpdateUI();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل النافذة: {ex.Message}", "خطأ",
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void UpdateUI()
    {
        if (fileInfoText != null)
        {
            fileInfoText.Text = playlist.Count > 0 ?
                $"قائمة التشغيل: {playlist.Count} ملف" :
                "جاهز للتشغيل - Ready to Play";
        }
    }

    private void InitializeTimer()
    {
        timer = new DispatcherTimer();
        timer.Interval = TimeSpan.FromSeconds(1);
        timer.Tick += Timer_Tick;
    }

    private void Timer_Tick(object? sender, EventArgs e)
    {
        if (mediaPlayer.Source != null && mediaPlayer.NaturalDuration.HasTimeSpan && !isDragging)
        {
            var currentTime = mediaPlayer.Position;
            var totalTime = mediaPlayer.NaturalDuration.TimeSpan;

            currentTimeText.Text = FormatTime(currentTime);
            totalTimeText.Text = FormatTime(totalTime);

            if (totalTime.TotalSeconds > 0)
            {
                progressSlider.Value = (currentTime.TotalSeconds / totalTime.TotalSeconds) * 100;
            }
        }
    }

    private string FormatTime(TimeSpan time)
    {
        return $"{(int)time.TotalMinutes:D2}:{time.Seconds:D2}";
    }

    // دوال السحب والإفلات
    private void MainWindow_DragEnter(object sender, DragEventArgs e)
    {
        if (e.Data.GetDataPresent(DataFormats.FileDrop))
        {
            e.Effects = DragDropEffects.Copy;
        }
        else
        {
            e.Effects = DragDropEffects.None;
        }
    }

    private void MainWindow_Drop(object sender, DragEventArgs e)
    {
        try
        {
            string[] files = (string[])e.Data.GetData(DataFormats.FileDrop);
            if (files != null && files.Length > 0)
            {
                LoadFiles(files);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل الملفات: {ex.Message}", "خطأ",
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    // اختصارات لوحة المفاتيح
    private void MainWindow_KeyDown(object sender, KeyEventArgs e)
    {
        switch (e.Key)
        {
            case Key.Space:
                PlayPause_Click(sender, e);
                break;
            case Key.S:
                Stop_Click(sender, e);
                break;
            case Key.O:
                if (Keyboard.Modifiers == ModifierKeys.Control)
                    OpenFile_Click(sender, e);
                break;
            case Key.Right:
                Next_Click(sender, e);
                break;
            case Key.Left:
                Previous_Click(sender, e);
                break;
            case Key.Up:
                VolumeUp();
                break;
            case Key.Down:
                VolumeDown();
                break;
        }
    }

    private void LoadFiles(string[] files)
    {
        var mediaFiles = files.Where(f => IsMediaFile(f)).ToList();
        if (mediaFiles.Any())
        {
            playlist.Clear();
            playlist.AddRange(mediaFiles);
            currentTrackIndex = 0;
            LoadCurrentTrack();
            UpdateUI();
        }
    }

    private bool IsMediaFile(string filePath)
    {
        var extension = Path.GetExtension(filePath).ToLower();
        return new[] { ".mp4", ".avi", ".wmv", ".mov", ".mkv", ".mp3", ".wav", ".wma", ".flac", ".m4a" }
               .Contains(extension);
    }

    private void OpenFile_Click(object sender, RoutedEventArgs e)
    {
        OpenFileDialog openFileDialog = new OpenFileDialog();
        openFileDialog.Multiselect = true;
        openFileDialog.Filter = "Media Files|*.mp4;*.avi;*.wmv;*.mov;*.mkv;*.mp3;*.wav;*.wma;*.flac;*.m4a|" +
                               "Video Files|*.mp4;*.avi;*.wmv;*.mov;*.mkv|" +
                               "Audio Files|*.mp3;*.wav;*.wma;*.flac;*.m4a|" +
                               "All Files|*.*";

        if (openFileDialog.ShowDialog() == true)
        {
            try
            {
                LoadFiles(openFileDialog.FileNames);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح الملف: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    private void LoadCurrentTrack()
    {
        if (playlist.Count > 0 && currentTrackIndex >= 0 && currentTrackIndex < playlist.Count)
        {
            try
            {
                var filePath = playlist[currentTrackIndex];
                mediaPlayer.Source = new Uri(filePath);
                mediaPlayer.LoadedBehavior = MediaState.Manual;

                Title = $"🎬 مشغل الوسائط المتطور 2025 - {Path.GetFileName(filePath)}";

                if (welcomeScreen != null)
                    welcomeScreen.Visibility = Visibility.Collapsed;

                if (videoControlsOverlay != null)
                    videoControlsOverlay.Visibility = Visibility.Visible;

                if (fileInfoText != null)
                    fileInfoText.Text = $"({currentTrackIndex + 1}/{playlist.Count}) {Path.GetFileName(filePath)}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الملف: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    // دوال التحكم الجديدة
    private void VolumeUp()
    {
        if (volumeSlider != null && volumeSlider.Value < 1.0)
        {
            volumeSlider.Value = Math.Min(1.0, volumeSlider.Value + 0.1);
        }
    }

    private void VolumeDown()
    {
        if (volumeSlider != null && volumeSlider.Value > 0.0)
        {
            volumeSlider.Value = Math.Max(0.0, volumeSlider.Value - 0.1);
        }
    }

    private void Previous_Click(object sender, RoutedEventArgs e)
    {
        if (playlist.Count > 0)
        {
            currentTrackIndex = currentTrackIndex > 0 ? currentTrackIndex - 1 : playlist.Count - 1;
            LoadCurrentTrack();
            if (isPlaying)
            {
                mediaPlayer.Play();
            }
        }
    }

    private void Next_Click(object sender, RoutedEventArgs e)
    {
        if (playlist.Count > 0)
        {
            if (isShuffleOn)
            {
                Random random = new Random();
                currentTrackIndex = random.Next(0, playlist.Count);
            }
            else
            {
                currentTrackIndex = currentTrackIndex < playlist.Count - 1 ? currentTrackIndex + 1 : 0;
            }
            LoadCurrentTrack();
            if (isPlaying)
            {
                mediaPlayer.Play();
            }
        }
    }

    private void Shuffle_Click(object sender, RoutedEventArgs e)
    {
        isShuffleOn = !isShuffleOn;
        if (sender is Button button)
        {
            button.Background = isShuffleOn ?
                System.Windows.Media.Brushes.Orange :
                (System.Windows.Media.Brush)new System.Windows.Media.BrushConverter().ConvertFrom("#FF404040");
        }
    }

    private void Repeat_Click(object sender, RoutedEventArgs e)
    {
        isRepeatOn = !isRepeatOn;
        if (sender is Button button)
        {
            button.Background = isRepeatOn ?
                System.Windows.Media.Brushes.Orange :
                (System.Windows.Media.Brush)new System.Windows.Media.BrushConverter().ConvertFrom("#FF404040");
        }
    }

    private void Playlist_Click(object sender, RoutedEventArgs e)
    {
        if (playlist.Count > 0)
        {
            string playlistText = "قائمة التشغيل:\n\n";
            for (int i = 0; i < playlist.Count; i++)
            {
                string marker = i == currentTrackIndex ? "▶️ " : "   ";
                playlistText += $"{marker}{i + 1}. {Path.GetFileName(playlist[i])}\n";
            }
            MessageBox.Show(playlistText, "قائمة التشغيل - Playlist",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }
        else
        {
            MessageBox.Show("قائمة التشغيل فارغة\nPlaylist is empty", "قائمة التشغيل - Playlist",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    private void Settings_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("الإعدادات قريباً!\nSettings coming soon!", "الإعدادات - Settings",
                      MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void Equalizer_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("المعادل قريباً!\nEqualizer coming soon!", "المعادل - Equalizer",
                      MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void PlayPause_Click(object sender, RoutedEventArgs e)
    {
        if (mediaPlayer.Source == null)
        {
            if (playlist.Count == 0)
            {
                MessageBox.Show("يرجى فتح ملف وسائط أولاً\nPlease open a media file first", "تنبيه - Notice",
                              MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }
            LoadCurrentTrack();
        }

        if (isPlaying)
        {
            mediaPlayer.Pause();
            UpdatePlayPauseButtons("▶️");
            timer?.Stop();
            isPlaying = false;
        }
        else
        {
            mediaPlayer.Play();
            UpdatePlayPauseButtons("⏸️");
            timer?.Start();
            isPlaying = true;
        }
    }

    private void UpdatePlayPauseButtons(string content)
    {
        if (playPauseButton != null)
            playPauseButton.Content = content;
        if (overlayPlayPauseButton != null)
            overlayPlayPauseButton.Content = content;
    }

    private void Stop_Click(object sender, RoutedEventArgs e)
    {
        mediaPlayer.Stop();
        playPauseButton.Content = "▶️";
        timer?.Stop();
        isPlaying = false;
        progressSlider.Value = 0;
        currentTimeText.Text = "00:00";
    }

    private void Exit_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }

    private void MediaPlayer_MediaOpened(object sender, RoutedEventArgs e)
    {
        if (mediaPlayer.NaturalDuration.HasTimeSpan)
        {
            totalTimeText.Text = FormatTime(mediaPlayer.NaturalDuration.TimeSpan);
        }
    }

    private void MediaPlayer_MediaEnded(object sender, RoutedEventArgs e)
    {
        Stop_Click(sender, e);
    }

    private void ProgressSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
    {
        if (isDragging && mediaPlayer.NaturalDuration.HasTimeSpan)
        {
            var totalSeconds = mediaPlayer.NaturalDuration.TimeSpan.TotalSeconds;
            var newPosition = TimeSpan.FromSeconds((progressSlider.Value / 100) * totalSeconds);
            mediaPlayer.Position = newPosition;
        }
    }

    private void ProgressSlider_DragStarted(object sender, DragStartedEventArgs e)
    {
        isDragging = true;
    }

    private void ProgressSlider_DragCompleted(object sender, DragCompletedEventArgs e)
    {
        isDragging = false;
        if (mediaPlayer.NaturalDuration.HasTimeSpan)
        {
            var totalSeconds = mediaPlayer.NaturalDuration.TimeSpan.TotalSeconds;
            var newPosition = TimeSpan.FromSeconds((progressSlider.Value / 100) * totalSeconds);
            mediaPlayer.Position = newPosition;
        }
    }

    private void VolumeSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
    {
        if (mediaPlayer != null)
        {
            mediaPlayer.Volume = volumeSlider.Value;
            volumeText.Text = $"{(int)(volumeSlider.Value * 100)}%";
        }
    }

    protected override void OnClosed(EventArgs e)
    {
        timer?.Stop();
        mediaPlayer?.Close();
        base.OnClosed(e);
    }
}