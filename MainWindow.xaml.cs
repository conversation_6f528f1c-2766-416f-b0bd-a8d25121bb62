﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Input;
using System.Windows.Threading;
using Microsoft.Win32;

namespace MediaPlayerApp;

/// <summary>
/// Advanced Media Player 2025 - مشغل الوسائط المتطور
/// </summary>
public partial class MainWindow : Window
{
    private DispatcherTimer? timer;
    private bool isDragging = false;
    private bool isPlaying = false;
    private List<string> playlist = new List<string>();
    private int currentTrackIndex = 0;

    public MainWindow()
    {
        InitializeComponent();
        InitializeTimer();
        InitializeEventHandlers();

        // تهيئة إعدادات المشغل
        this.Loaded += MainWindow_Loaded;
    }

    private void InitializeEventHandlers()
    {
        // إضافة دعم السحب والإفلات
        this.AllowDrop = true;
        this.Drop += MainWindow_Drop;
        this.DragEnter += MainWindow_DragEnter;

        // إضافة اختصارات لوحة المفاتيح
        this.KeyDown += MainWindow_KeyDown;
    }

    private void MainWindow_Loaded(object sender, RoutedEventArgs e)
    {
        try
        {
            // تهيئة إعدادات المشغل مع فحص null
            if (mediaPlayer != null)
                mediaPlayer.Volume = 0.5;

            if (volumeSlider != null)
                volumeSlider.Value = 0.5;

            MessageBox.Show("🎬 مشغل الوسائط المتطور 2025 جاهز!\nAdvanced Media Player 2025 Ready!",
                           "مرحباً - Welcome", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل النافذة: {ex.Message}\n\nDetails: {ex.StackTrace}", "خطأ",
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void InitializeTimer()
    {
        timer = new DispatcherTimer();
        timer.Interval = TimeSpan.FromSeconds(1);
        timer.Tick += Timer_Tick;
    }

    private void Timer_Tick(object? sender, EventArgs e)
    {
        try
        {
            if (mediaPlayer?.Source != null && mediaPlayer.NaturalDuration.HasTimeSpan && !isDragging)
            {
                var currentTime = mediaPlayer.Position;
                var totalTime = mediaPlayer.NaturalDuration.TimeSpan;

                if (currentTimeText != null)
                    currentTimeText.Text = FormatTime(currentTime);

                if (totalTimeText != null)
                    totalTimeText.Text = FormatTime(totalTime);

                if (totalTime.TotalSeconds > 0 && progressSlider != null)
                {
                    progressSlider.Value = (currentTime.TotalSeconds / totalTime.TotalSeconds) * 100;
                }
            }
        }
        catch (Exception ex)
        {
            // تجاهل أخطاء Timer لتجنب تعطيل التطبيق
        }
    }

    private string FormatTime(TimeSpan time)
    {
        return $"{(int)time.TotalMinutes:D2}:{time.Seconds:D2}";
    }

    // دوال السحب والإفلات
    private void MainWindow_DragEnter(object sender, DragEventArgs e)
    {
        if (e.Data.GetDataPresent(DataFormats.FileDrop))
        {
            e.Effects = DragDropEffects.Copy;
        }
        else
        {
            e.Effects = DragDropEffects.None;
        }
    }

    private void MainWindow_Drop(object sender, DragEventArgs e)
    {
        try
        {
            string[] files = (string[])e.Data.GetData(DataFormats.FileDrop);
            if (files != null && files.Length > 0)
            {
                LoadFiles(files);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل الملفات: {ex.Message}", "خطأ",
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    // اختصارات لوحة المفاتيح
    private void MainWindow_KeyDown(object sender, KeyEventArgs e)
    {
        switch (e.Key)
        {
            case Key.Space:
                PlayPause_Click(sender, e);
                break;
            case Key.S:
                Stop_Click(sender, e);
                break;
            case Key.O:
                if (Keyboard.Modifiers == ModifierKeys.Control)
                    OpenFile_Click(sender, e);
                break;
            case Key.Right:
                Next_Click(sender, e);
                break;
            case Key.Left:
                Previous_Click(sender, e);
                break;
            case Key.Up:
                if (volumeSlider != null && volumeSlider.Value < 1.0)
                    volumeSlider.Value = Math.Min(1.0, volumeSlider.Value + 0.1);
                break;
            case Key.Down:
                if (volumeSlider != null && volumeSlider.Value > 0.0)
                    volumeSlider.Value = Math.Max(0.0, volumeSlider.Value - 0.1);
                break;
        }
    }

    private void LoadFiles(string[] files)
    {
        var mediaFiles = files.Where(f => IsMediaFile(f)).ToList();
        if (mediaFiles.Any())
        {
            playlist.Clear();
            playlist.AddRange(mediaFiles);
            currentTrackIndex = 0;
            LoadCurrentTrack();
        }
    }

    private bool IsMediaFile(string filePath)
    {
        var extension = Path.GetExtension(filePath).ToLower();
        return new[] { ".mp4", ".avi", ".wmv", ".mov", ".mkv", ".mp3", ".wav", ".wma", ".flac", ".m4a" }
               .Contains(extension);
    }

    private void OpenFile_Click(object sender, RoutedEventArgs e)
    {
        OpenFileDialog openFileDialog = new OpenFileDialog();
        openFileDialog.Multiselect = true;
        openFileDialog.Filter = "Media Files|*.mp4;*.avi;*.wmv;*.mov;*.mkv;*.mp3;*.wav;*.wma;*.flac;*.m4a|" +
                               "Video Files|*.mp4;*.avi;*.wmv;*.mov;*.mkv|" +
                               "Audio Files|*.mp3;*.wav;*.wma;*.flac;*.m4a|" +
                               "All Files|*.*";

        if (openFileDialog.ShowDialog() == true)
        {
            try
            {
                LoadFiles(openFileDialog.FileNames);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح الملف: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    private void LoadCurrentTrack()
    {
        if (playlist.Count > 0 && currentTrackIndex >= 0 && currentTrackIndex < playlist.Count)
        {
            try
            {
                var filePath = playlist[currentTrackIndex];
                if (mediaPlayer != null)
                {
                    mediaPlayer.Source = new Uri(filePath);
                    mediaPlayer.LoadedBehavior = MediaState.Manual;
                }

                Title = $"🎬 مشغل الوسائط المتطور 2025 - {Path.GetFileName(filePath)}";

                if (welcomeScreen != null)
                    welcomeScreen.Visibility = Visibility.Collapsed;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الملف: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    // دوال التحكم المبسطة
    private void Previous_Click(object sender, RoutedEventArgs e)
    {
        if (playlist.Count > 0)
        {
            currentTrackIndex = currentTrackIndex > 0 ? currentTrackIndex - 1 : playlist.Count - 1;
            LoadCurrentTrack();
            if (isPlaying)
            {
                mediaPlayer.Play();
            }
        }
    }

    private void Next_Click(object sender, RoutedEventArgs e)
    {
        if (playlist.Count > 0)
        {
            currentTrackIndex = currentTrackIndex < playlist.Count - 1 ? currentTrackIndex + 1 : 0;
            LoadCurrentTrack();
            if (isPlaying)
            {
                mediaPlayer.Play();
            }
        }
    }

    private void PlayPause_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (mediaPlayer?.Source == null)
            {
                if (playlist.Count == 0)
                {
                    MessageBox.Show("يرجى فتح ملف وسائط أولاً\nPlease open a media file first", "تنبيه - Notice",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }
                LoadCurrentTrack();
            }

            if (isPlaying)
            {
                mediaPlayer?.Pause();
                if (playPauseButton != null)
                    playPauseButton.Content = "▶️";
                timer?.Stop();
                isPlaying = false;
            }
            else
            {
                mediaPlayer?.Play();
                if (playPauseButton != null)
                    playPauseButton.Content = "⏸️";
                timer?.Start();
                isPlaying = true;
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في التشغيل: {ex.Message}", "خطأ",
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void Stop_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            mediaPlayer?.Stop();
            if (playPauseButton != null)
                playPauseButton.Content = "▶️";
            timer?.Stop();
            isPlaying = false;
            if (progressSlider != null)
                progressSlider.Value = 0;
            if (currentTimeText != null)
                currentTimeText.Text = "00:00";
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في الإيقاف: {ex.Message}", "خطأ",
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void Exit_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }

    private void MediaPlayer_MediaOpened(object sender, RoutedEventArgs e)
    {
        if (mediaPlayer.NaturalDuration.HasTimeSpan)
        {
            totalTimeText.Text = FormatTime(mediaPlayer.NaturalDuration.TimeSpan);
        }
    }

    private void MediaPlayer_MediaEnded(object sender, RoutedEventArgs e)
    {
        Stop_Click(sender, e);
    }

    private void ProgressSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
    {
        if (isDragging && mediaPlayer.NaturalDuration.HasTimeSpan)
        {
            var totalSeconds = mediaPlayer.NaturalDuration.TimeSpan.TotalSeconds;
            var newPosition = TimeSpan.FromSeconds((progressSlider.Value / 100) * totalSeconds);
            mediaPlayer.Position = newPosition;
        }
    }

    private void ProgressSlider_DragStarted(object sender, DragStartedEventArgs e)
    {
        isDragging = true;
    }

    private void ProgressSlider_DragCompleted(object sender, DragCompletedEventArgs e)
    {
        isDragging = false;
        if (mediaPlayer.NaturalDuration.HasTimeSpan)
        {
            var totalSeconds = mediaPlayer.NaturalDuration.TimeSpan.TotalSeconds;
            var newPosition = TimeSpan.FromSeconds((progressSlider.Value / 100) * totalSeconds);
            mediaPlayer.Position = newPosition;
        }
    }

    private void VolumeSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
    {
        try
        {
            if (mediaPlayer != null && volumeSlider != null)
            {
                mediaPlayer.Volume = volumeSlider.Value;
                if (volumeText != null)
                    volumeText.Text = $"{(int)(volumeSlider.Value * 100)}%";
            }
        }
        catch (Exception ex)
        {
            // تجاهل أخطاء تغيير الصوت
        }
    }

    protected override void OnClosed(EventArgs e)
    {
        timer?.Stop();
        mediaPlayer?.Close();
        base.OnClosed(e);
    }
}