<!--  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - >
<  view.html: VLC media player web interface - VLM
< - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - >
<  Copyright (C) 2005-2014 VLC authors and VideoLAN
<
<  Authors: <AUTHORS>
<
<  This program is free software; you can redistribute it and/or modify
<  it under the terms of the GNU General Public License as published by
<  the Free Software Foundation; either version 2 of the License, or
<  (at your option) any later version.
<
<  This program is distributed in the hope that it will be useful,
<  but WITHOUT ANY WARRANTY; without even the implied warranty of
<  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
<  GNU General Public License for more details.
<
<  You should have received a copy of the GNU General Public License
<  along with this program; if not, write to the Free Software
<  Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston MA 02110-1301, USA.
< - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -->
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
	<title><?vlc gettext("VLC media player - Flash Viewer") ?></title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
	<meta name="referrer" content="no-referrer" />
	<link href="favicon.ico" type="image/x-icon" rel="shortcut icon"/>
	<link type="text/css" href="css/ui-lightness/jquery-ui-1.8.13.custom.css" rel="stylesheet" />
	<link type="text/css" href="css/main.css" rel="stylesheet" />
	<script type="text/javascript" src="js/common.js"></script>
	<script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.6.1/jquery.min.js"></script>
	<script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.8.13/jquery-ui.min.js"></script>
	<script type="text/javascript" src="https://releases.flowplayer.org/js/flowplayer-3.2.6.min.js"></script>
	<script type="text/javascript">
	//<![CDATA[
	$(function(){
		$('#window_stream').resizable({
			minHeight: 300,
			minWidth: 400,
			resize:function(event,ui){
				$('#player').css({
					'width':ui.size.width-80,
					'height':ui.size.height-100
				})
			}
		});
		$('#button_stream_config').click(function(){
			$('#window_stream_config').dialog('open');
		});
		$('#player').empty();
		$('#player').attr('href',$('#stream_protocol').val()+'://'+$('#stream_host').val()+':'+$('#stream_port').val()+'/'+$('#stream_file').val());
		flowplayer("player", "https://releases.flowplayer.org/swf/flowplayer-3.2.7.swf");
	});
	//]]>
	</script>
	<style>
	#window_stream { width: 800px; height: 600px; padding: 0.5em; }
	#window_stream h3 { text-align: left; margin: 0; font-weight: normal; font-size: 12px }
	</style>
</head>

<body>
	<div id="window_stream" class="ui-widget-content">
		<h3 class="ui-widget-header"><?vlc gettext("Streaming Output") ?></h3>
		<div class="ui-widget-content">
			<div id="button_stream_config" class="button icon ui-widget ui-state-default" title="<?vlc gettext("Configure") ?>" opendialog="window_stream_config"><span class="ui-icon ui-icon-wrench"></span></div>
			<div align="center">
				<div href="http://localhost:8081/stream.flv" style="display:block;width:720px;height:500px" id="player"></div>
			</div>
			<div>&nbsp;</div>
		</div>
		<div class="footer">
			<?vlc print(vlc.misc.version() .. " - Lua Web Interface - " .. vlc.misc.copyright()) ?>
		</div>
	</div>
	<?vlc

	dialogs("stream_config_window.html");
	?>
</body>
</html>
